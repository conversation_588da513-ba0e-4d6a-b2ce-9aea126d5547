<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.inngke.ai</groupId>
    <artifactId>crm_ai_yk_bootstrap</artifactId>
    <version>2.0.0-SNAPSHOT</version>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <crm_ai_yk_server.version>2.0.0-SNAPSHOT</crm_ai_yk_server.version>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <crm_ai_yk_server.version>1.3.73</crm_ai_yk_server.version>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
    </profiles>

    <properties>
        <app.id>crm_ai_yk</app.id>
        <spring-boot.version>2.6.3</spring-boot.version>

        <jdk.version>11</jdk.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.inngke.ai</groupId>
            <artifactId>crm_ai_yk_server</artifactId>
            <version>${crm_ai_yk_server.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- 重要！Duo-Doc，通过解析源码、注释自动生成接口信息 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.3.1</version>
                <configuration>
                    <doclet>com.duoec.doc.DuoDoclet</doclet>
                    <docletArtifact>
                        <groupId>com.duoec</groupId>
                        <artifactId>duo-doclet-11</artifactId>
                        <version>1.0.0-SNAPSHOT</version>
                    </docletArtifact>
                    <useStandardDocletOptions>false</useStandardDocletOptions>

                    <includeDependencySources>true</includeDependencySources>
                    <includeTransitiveDependencySources>true</includeTransitiveDependencySources>
                    <dependencySourceIncludes>
                        <dependencySourceInclude>com.inngke*:*</dependencySourceInclude>
                    </dependencySourceIncludes>

                    <additionalOptions>
                        <additionalOption>-doc.basePath ${project.basedir}</additionalOption>
                        <additionalOption>-doc.markdown.readme ../README.md</additionalOption>
                        <additionalOption>-doc.appId ${app.id}</additionalOption>
                        <additionalOption>-doc.markdown.path ../doc</additionalOption>
                        <additionalOption>-doc.exporter.server https://doc.inngke.com</additionalOption>
                    </additionalOptions>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <!-- package可以在提交代码后由CI自动触发，如果不需要自动触发，可以设置为site，届时需要手工执行：mvn clean site -->
                        <phase>compile</phase>
                        <goals>
                            <goal>javadoc</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>inngke-java-maven</id>
            <name>maven</name>
            <url>https://inngke-maven.pkg.coding.net/repository/java/maven/</url>
        </repository>
    </distributionManagement>
</project>