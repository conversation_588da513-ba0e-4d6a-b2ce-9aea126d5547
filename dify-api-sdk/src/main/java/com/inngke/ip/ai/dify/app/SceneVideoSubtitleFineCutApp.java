package com.inngke.ip.ai.dify.app;

import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.app.dto.SceneVideoSubtitleCutResp;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import retrofit2.Response;

import java.util.List;
import java.util.Map;

/**
 * [V5]短视频-口播快剪-单素材检索
 * http://**************/app/74f307f5-49db-4785-821b-4b90dbb46cf3/workflow
 */
@Component
public class SceneVideoSubtitleFineCutApp extends BaseDifyWorkflowApp<List<SceneVideoSubtitleCutResp>> {
    private static final Logger logger = LoggerFactory.getLogger(SceneVideoSubtitleFineCutApp.class);

    /**
     * 工作流任务名称
     *
     * @demo 批量创作视频脚本
     */
    @Override
    protected String taskName() {
        return "口播字幕裁剪";
    }

    @Override
    protected List<SceneVideoSubtitleCutResp> parseResponse(DifyWorkflowRequest request, Response<DifyWorkflowResponse> response, Map<String, Object> outputs) {
        String correctScriptsJson = (String) outputs.get("correctScriptsJSON");
        List<SceneVideoSubtitleCutResp> items = JsonUtil.jsonToList(correctScriptsJson, SceneVideoSubtitleCutResp.class);
        if (CollectionUtils.isEmpty(items)) {
            logger.warn("响应为空！");
        }
        return items;
    }
}
