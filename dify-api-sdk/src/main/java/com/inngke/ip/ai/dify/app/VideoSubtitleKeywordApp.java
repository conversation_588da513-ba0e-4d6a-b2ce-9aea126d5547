package com.inngke.ip.ai.dify.app;

import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.util.Map;

/**
 * [V5]字幕高亮-GenerateWithKnowledge
 * http://**************/app/1286bb28-1307-4c1e-9bc7-d2b2d4530b38/workflow
 */
@Component
public class VideoSubtitleKeywordApp extends BaseDifyWorkflowApp<String> {
    @Override
    protected String taskName() {
        return "字幕高亮";
    }

    @Override
    protected String parseResponse(DifyWorkflowRequest request, Response response, Map outputs) {
        return (String) outputs.get("text");
    }
}
