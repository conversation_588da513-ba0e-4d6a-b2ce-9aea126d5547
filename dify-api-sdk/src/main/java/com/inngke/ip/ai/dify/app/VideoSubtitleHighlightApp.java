package com.inngke.ip.ai.dify.app;

import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.util.Map;

/**
 * [V5]短视频-手工划重点修复
 * http://**************/app/f6a90907-4a70-4fc6-bca7-77ff9c141d1c/workflow
 */
@Component
public class VideoSubtitleHighlightApp extends  BaseDifyWorkflowApp<String>{

    /**
     * 工作流任务名称
     *
     * @demo 批量创作视频脚本
     */
    @Override
    protected String taskName() {
        return "手工字幕高亮";
    }

    @Override
    protected String parseResponse(DifyWorkflowRequest request, Response<DifyWorkflowResponse> response, Map<String, Object> outputs) {
        Object taggedSubtitle = outputs.get("keyscripts");
        if (taggedSubtitle == null) {
            throw new InngkeServiceException("手工字幕高亮失败，keyscripts！");
        }

        return taggedSubtitle.toString();
    }
}
