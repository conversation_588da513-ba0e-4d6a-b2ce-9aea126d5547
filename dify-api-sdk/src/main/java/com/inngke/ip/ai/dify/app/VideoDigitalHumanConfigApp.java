package com.inngke.ip.ai.dify.app;

import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.app.dto.DigitalPersonRolesDto;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import retrofit2.Response;

import java.util.Map;

/**
 * 获取脚本数字人配置
 * [V5]视频脚本类型识别
 * http://**************/app/efe8610b-8b60-49da-a371-83390584776b/workflow
 * 参数：
 * script: 原始脚本
 */
@Component
public class VideoDigitalHumanConfigApp extends BaseDifyWorkflowApp<DigitalPersonRolesDto> {
    /**
     * 工作流任务名称
     *
     * @demo 批量创作视频脚本
     */
    @Override
    protected String taskName() {
        return "脚本识别";
    }

    @Override
    protected DigitalPersonRolesDto parseResponse(DifyWorkflowRequest request, Response<DifyWorkflowResponse> response, Map<String, Object> outputs) {
        String resultStr = outputs.get("text").toString();
        String formattedSubtitle = (String) outputs.get("formattedSubtitle");
        DigitalPersonRolesDto digitalPersonRolesDto = JsonUtil.jsonToObject(resultStr, DigitalPersonRolesDto.class);
        int roleSize = CollectionUtils.isEmpty(digitalPersonRolesDto.getScriptsRole()) ? 1 : digitalPersonRolesDto.getScriptsRole().size();
        digitalPersonRolesDto.setScriptsType(roleSize);
        digitalPersonRolesDto.setFormattedSubtitle(formattedSubtitle);
        return digitalPersonRolesDto;
    }
}
