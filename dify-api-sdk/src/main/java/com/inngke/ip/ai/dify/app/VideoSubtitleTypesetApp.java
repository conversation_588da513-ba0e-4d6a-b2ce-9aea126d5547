package com.inngke.ip.ai.dify.app;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ai.dto.SubtitleDto;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.api.DifyWorkflowApi;
import com.inngke.ip.ai.dify.app.dto.DifyVideoContentDto;
import com.inngke.ip.ai.dify.app.dto.DifyVideoScriptsResp;
import com.inngke.ip.ai.dify.app.dto.SubtitleGroup;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import retrofit2.Response;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * [V5]短视频-文案字幕排版
 * http://**************/app/a4bdccaf-b882-4364-9f81-18263362f8c6/workflow
 * 参数：
 * script: 原始脚本
 * class_name: 视频类型
 */
@Component
public class VideoSubtitleTypesetApp extends BaseDifyWorkflowApp<DifyVideoScriptsResp> {
    public static final Set<String> MARKS = Sets.newHashSet(" ", "　", ", ", "。", "？", "！", "、", "；", "”", "’", "）", "》", "】", "……", "——", "～", "…", "＞", "｝", "］", ",", "?", "!", ";", ")", ">", "]", "}", "…", "-", "~", "，");
    @Autowired
    private DifyWorkflowApi difyWorkflowApi;

    /**
     * 工作流任务名称
     *
     * @demo 批量创作视频脚本
     */
    @Override
    protected String taskName() {
        return "创建分镜";
    }

    @Override
    protected DifyVideoScriptsResp parseResponse(DifyWorkflowRequest request, Response<DifyWorkflowResponse> response, Map<String, Object> outputs) {
        //获取，并去除\u00A0符号
        Object scriptMakeVal = outputs.get("scriptMake");
        if (scriptMakeVal == null) {
            throw new InngkeServiceException("脚本生成失败，scriptMake为空！");
        }

        String scriptMake = scriptMakeVal.toString().replace("\u00A0", InngkeAppConst.WHITE_SPACE_STR);
        List<SubtitleGroup> scriptCreateResp = JsonUtil.jsonToList(scriptMake, SubtitleGroup.class);
        if (CollectionUtils.isEmpty(scriptCreateResp)) {
            throw new InngkeServiceException("脚本生成失败，scriptMake为空数组！");
        }
        // 检查分镜完整性
        List<Integer> boardNos = scriptCreateResp.stream().map(SubtitleGroup::getBoardNo).collect(Collectors.toList());
        for (int i = 1; i < boardNos.get(boardNos.size() - 1); i++) {
            if (!boardNos.contains(i)) {
                throw new InngkeServiceException("分镜[" + i + "]丢失！");
            }
        }

        Map<String, Integer> roles = Maps.newHashMap();

        List<SubtitleDto> subtitles = Lists.newArrayList();
        List<String> rolesList = Lists.newArrayList();
        scriptCreateResp.forEach(subItem -> {
            int roleIndex = roles.computeIfAbsent(subItem.getSpeaker(), role -> {
                rolesList.add(role);
                return roles.size();
            });
            int scriptIndex = subItem.getBoardNo() - 1;
            SubtitleDto lastSubtitle = null;
            for (String text : subItem.getSubtitle().split(VideoScriptSceneApp.SUBTITLE_SPLITTER_VERSION_BACKSLASH)) {
                text = text.trim();
                if (text.isBlank()) {
                    continue;
                }
                if (text.length() == 1 && isMark(text)) {
                    //如果是一个字符的符号，则合入上一字幕
                    if (lastSubtitle != null) {
                        lastSubtitle.setText(lastSubtitle.getText() + text);
                    }
                    continue;
                }
                SubtitleDto subtitle = new SubtitleDto();
                subtitle.setScriptIndex(scriptIndex);
                subtitle.setText(text);
                subtitle.setRole(roleIndex);
                subtitles.add(subtitle);
                lastSubtitle = subtitle;
            }
        });

//        if (roles.size() > 2) {
//            throw new InngkeServiceException("暂不支持" + (roles.size() + 1) + "人对话");
//        }

        DifyVideoScriptsResp script = new DifyVideoScriptsResp();

        String scriptBig = (String) outputs.get("scriptBig");

        String scriptContent = (String) outputs.get("scriptContent");
        if (StringUtils.hasLength(scriptContent)) {
            DifyVideoContentDto contentDto = JsonUtil.jsonToObject(scriptContent, DifyVideoContentDto.class);
            if (contentDto != null) {
                script.setCover(contentDto.getCover())
                        .setTags(contentDto.getTags())
                        .setSummary(contentDto.getSummary())
                        .setTitle(contentDto.getTitle());
            }
        }

        script.setRoles(rolesList);
        script.setSubtitles(subtitles);
        script.setBigTitle(scriptBig);
        return script;
    }

    private boolean isMark(String subtitle) {
        return MARKS.contains(subtitle);
    }
}
