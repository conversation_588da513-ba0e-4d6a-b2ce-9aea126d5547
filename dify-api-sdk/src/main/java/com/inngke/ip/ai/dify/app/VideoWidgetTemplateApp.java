package com.inngke.ip.ai.dify.app;

import com.inngke.ai.dto.widget.WidgetGroup;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import retrofit2.Response;

import java.util.Map;

/**
 * [V5]短视频-合成视频贴片
 * http://**************/app/f089a9dd-8812-4919-bc57-690dd718dc24/workflow
 */
@Component
public class VideoWidgetTemplateApp extends BaseDifyWorkflowApp<WidgetGroup>{
    @Override
    protected String taskName() {
        return "智能贴片";
    }

    @Override
    protected String appKey() {
        return "app-wQPFmKO90S6mRa8NoqX3jEK6";
    }

    @Override
    protected WidgetGroup parseResponse(DifyWorkflowRequest request, Response<DifyWorkflowResponse> response, Map<String, Object> outputs) {
        String widgetGroupJson = outputs.get("text").toString();
        if (!StringUtils.hasLength(widgetGroupJson)) {
            return null;
        }

        widgetGroupJson = widgetGroupJson
                .replace("```json", InngkeAppConst.EMPTY_STR)
                .replace("```", InngkeAppConst.EMPTY_STR);

        return JsonUtil.jsonToObject(widgetGroupJson, WidgetGroup.class);
    }
}
