package com.inngke.ip.ai.dify.dto;

import java.io.Serializable;

public class DifyAppConfig implements Serializable {
    /**
     * 应用ID，即类名
     */
    private String id;

    /**
     * 应用名称，方便查找
     */
    private String name;

    /**
     * 应用AppKey
     */
    private String appKey;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }
}
