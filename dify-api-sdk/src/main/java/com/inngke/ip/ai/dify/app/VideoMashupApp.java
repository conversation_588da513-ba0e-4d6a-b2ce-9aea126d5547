package com.inngke.ip.ai.dify.app;

import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.app.dto.VideoMashupDto;
import com.inngke.ip.ai.dify.app.dto.VideoMashupResponse;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.util.List;
import java.util.Map;

/**
 * [V5]视频批量混编
 * http://**************/app/0565093a-4879-49a8-bbed-76922122e1f1/workflow
 */
@Component
public class VideoMashupApp extends BaseDifyWorkflowApp<VideoMashupResponse> {
    @Override
    protected String taskName() {
        return "成品裂变";
    }

    @Override
    protected VideoMashupResponse parseResponse(DifyWorkflowRequest request, Response<DifyWorkflowResponse> response, Map<String, Object> outputs) {
        Object output = outputs.get("output");
        List<VideoMashupDto> videoMashupDtos = JsonUtil.jsonToList(output.toString(), VideoMashupDto.class);

        VideoMashupResponse videoMashupResponse = new VideoMashupResponse();

        videoMashupResponse.setMashupList(videoMashupDtos);

        return videoMashupResponse;
    }
}
