package com.inngke.ip.ai.dify.app;

import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.RetrofitUtils;
import com.inngke.ip.ai.dify.api.DifyWorkflowApi;
import com.inngke.ip.ai.dify.config.DifyProperties;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowData;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import com.inngke.ip.ai.dify.enums.DifyResponseModeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import retrofit2.Response;

import java.io.IOException;
import java.util.Map;

public abstract class BaseDifyWorkflowApp<T> {
    private static final Logger logger = LoggerFactory.getLogger(BaseDifyWorkflowApp.class);

    private String key = InngkeAppConst.EMPTY_STR;

    @Autowired
    private DifyWorkflowApi difyWorkflowApi;

    /**
     * 工作流任务名称
     *
     * @demo 批量创作视频脚本
     */
    protected abstract String taskName();

    /**
     * 获取工作流接口调用 key，不包含 Bearer
     */
    protected String appKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    /**
     * 构建工作流接口请求
     *
     * @param userId 执行用户id
     * @param inputs 输入参数
     */
    protected DifyWorkflowRequest createRequest(String userId, Map<String, String> inputs) {
        DifyWorkflowRequest workflowRequest = new DifyWorkflowRequest();
        workflowRequest.setInputs(inputs);
        workflowRequest.setResponseMode(DifyResponseModeEnum.BLOCKING.getType());
        workflowRequest.setUser(userId);
        return workflowRequest;
    }

    protected abstract T parseResponse(DifyWorkflowRequest request, Response<DifyWorkflowResponse> response, Map<String, Object> outputs);

    public T execute(String userId, Map<String, String> inputs) {
        if (!StringUtils.hasLength(userId)) {
            throw new InngkeServiceException("工作流调用者参数userId不能为空！");
        }
        if (inputs == null) {
            throw new InngkeServiceException("工作流参数inputs不能为空！");
        }
        DifyWorkflowRequest request = createRequest(userId, inputs);
        Response<DifyWorkflowResponse> response;
        logger.info("{}请求：{}", taskName(), JsonUtil.toJsonString(request));
        try {
            response = difyWorkflowApi.run(DifyProperties.STR_BEARER + appKey(), request).execute();
        } catch (IOException e) {
            throw new InngkeServiceException(e);
        }
        DifyWorkflowResponse body = RetrofitUtils.getResponse(response, taskName());
        DifyWorkflowData data = body.getData();
        Map<String, Object> outputs = data.getOutputs();
        if (CollectionUtils.isEmpty(outputs)) {
            throw new InngkeServiceException(taskName() + "失败，output为空：" + JsonUtil.toJsonString(data));
        }
        logger.info("{}响应：{}", taskName(), JsonUtil.toJsonString(outputs));

        return parseResponse(request, response, outputs);
    }
}
