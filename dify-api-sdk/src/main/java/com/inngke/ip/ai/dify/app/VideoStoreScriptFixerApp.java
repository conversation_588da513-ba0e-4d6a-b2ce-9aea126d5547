package com.inngke.ip.ai.dify.app;

import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import com.inngke.ip.ai.dify.dto.response.DifyWorkflowResponse;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.util.Map;

/**
 * [V]短视频-剧情脚本修复
 * http://**************/app/3f293495-5646-411c-b8cb-8460ab6a9af9/workflow
 * 参数：
 * script: 脚本
 * type: 类型
 */
@Component
public class VideoStoreScriptFixerApp extends BaseDifyWorkflowApp<String> {

    @Override
    protected String taskName() {
        return "剧情脚本修复";
    }

    @Override
    protected String parseResponse(DifyWorkflowRequest request, Response<DifyWorkflowResponse> response, Map<String, Object> outputs) {
        Object correctScriptsObj = outputs.get("correctScripts");
        if (correctScriptsObj == null) {
            throw new InngkeServiceException("剧情脚本修复失败，correctScripts为空！");
        }

        return correctScriptsObj.toString();
    }
}
