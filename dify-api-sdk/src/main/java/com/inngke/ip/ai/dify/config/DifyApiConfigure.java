package com.inngke.ip.ai.dify.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.ip.ai.dify.api.DifyApi;
import com.inngke.ip.ai.dify.api.DifyDatasetApi;
import com.inngke.ip.ai.dify.api.DifyWorkflowApi;
import com.inngke.ip.ai.dify.app.BaseDifyWorkflowApp;
import com.inngke.ip.ai.dify.dto.DifyAppConfig;
import okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;
import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Configuration
public class DifyApiConfigure {
    private static final Logger logger = LoggerFactory.getLogger(DifyApiConfigure.class);

    /**
     * 需要单独配置一个超时时间长一点的
     */
    @Bean
    public OkHttpClient difyHttpClient() {
        return new OkHttpClient.Builder()
                .retryOnConnectionFailure(false)
                .callTimeout(60*10, TimeUnit.SECONDS)
                .connectTimeout(60*10, TimeUnit.SECONDS)
                .readTimeout(60*10, TimeUnit.SECONDS)
                .writeTimeout(60*10, TimeUnit.SECONDS)
                .build();
    }

    @Bean
    public Retrofit getDifyRetrofitClient(ObjectMapper objectMapper, DifyProperties difyProperties) {
        return new Retrofit.Builder()
                .baseUrl(difyProperties.getUrl())
                .addConverterFactory(JacksonConverterFactory.create(objectMapper))
                .client(difyHttpClient())
                .build();
    }

    @Bean
    public DifyApi difyApi(@Qualifier("getDifyRetrofitClient") Retrofit retrofit) {
        return retrofit.create(DifyApi.class);
    }

    @Bean
    public DifyDatasetApi difyDatesetApi(@Qualifier("getDifyRetrofitClient") Retrofit retrofit) {
        return retrofit.create(DifyDatasetApi.class);
    }

    @Bean
    public DifyWorkflowApi difyWorkflowApi(@Qualifier("getDifyRetrofitClient") Retrofit retrofit) {
        return retrofit.create(DifyWorkflowApi.class);
    }

    public static void setAppKeys(Map<String, DifyAppConfig> configs, List<BaseDifyWorkflowApp> apps) {
        if (CollectionUtils.isEmpty(configs)) {
            logger.error("未配置 dify应用");
            return;
        }
        apps.forEach(app -> {
            String appId = app.getClass().getSimpleName();
            DifyAppConfig config = configs.get(appId);
            if (config == null) {
                logger.error("未配置 dify应用: {}", appId);
                throw new InngkeServiceException("未配置 dify应用: " + appId);
            }
            app.setKey(config.getAppKey());
        });
    }
}
