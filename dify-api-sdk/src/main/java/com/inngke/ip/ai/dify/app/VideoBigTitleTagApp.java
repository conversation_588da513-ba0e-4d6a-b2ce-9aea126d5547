package com.inngke.ip.ai.dify.app;

import com.inngke.ip.ai.dify.dto.request.DifyWorkflowRequest;
import org.springframework.stereotype.Component;
import retrofit2.Response;

import java.util.Map;

/**
 * [V5]大字报
 * http://**************/app/649c2c72-0bf3-4720-8897-30162faea3cb/workflow
 */
@Component
public class VideoBigTitleTagApp extends BaseDifyWorkflowApp<String> {

    @Override
    protected String taskName() {
        return "[V5]大字报";
    }

    @Override
    protected String appKey() {
        return "app-IsaftwbEAUPzQqgocJvJyuqs";
    }

    @Override
    protected String parseResponse(DifyWorkflowRequest request, Response response, Map outputs) {
        return (String) outputs.get("big_character");
    }
}
