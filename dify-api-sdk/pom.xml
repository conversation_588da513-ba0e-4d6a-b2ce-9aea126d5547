<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.inngke.ip</groupId>
    <artifactId>dify-api-sdk</artifactId>
    <version>${dify-api-sdk.version}</version>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <dify-api-sdk.version>2.0.1-SNAPSHOT</dify-api-sdk.version>

                <video_ai_yk_api.version>2.0.3-SNAPSHOT</video_ai_yk_api.version>
                <yk-common-app.version>3.0.0-SNAPSHOT</yk-common-app.version>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
<!--                <dify-api-sdk.version>2.0.0-SNAPSHOT</dify-api-sdk.version>-->
                <dify-api-sdk.version>1.0.17</dify-api-sdk.version>

                <video_ai_yk_api.version>1.0.59</video_ai_yk_api.version>
                <yk-common-app.version>3.1.14</yk-common-app.version>
            </properties>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
    </profiles>

    <properties>
        <jdk.version>21</jdk.version>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>

        <retrofit-spring-boot-starter.version>2.2.11</retrofit-spring-boot-starter.version>
        <jackson-annotations.version>2.12.2</jackson-annotations.version>
        <spring.version>5.3.33</spring.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.inngke.common</groupId>
            <artifactId>yk-common-app</artifactId>
            <version>${yk-common-app.version}</version>
        </dependency>

        <dependency>
            <groupId>com.inngke.ai</groupId>
            <artifactId>video_ai_yk_api</artifactId>
            <version>${video_ai_yk_api.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.github.lianjiatech</groupId>
            <artifactId>retrofit-spring-boot-starter</artifactId>
            <version>${retrofit-spring-boot-starter.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-annotations.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp-sse</artifactId>
            <version>3.14.9</version>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.10</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
            <version>${spring.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot</artifactId>
            <version>2.2.6.RELEASE</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.5.2</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.0.2</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <distributionManagement>
        <repository>
            <id>inngke-java-maven</id>
            <name>maven</name>
            <url>https://inngke-maven.pkg.coding.net/repository/java/maven/</url>
        </repository>
    </distributionManagement>
</project>