/*
 * create by mybatis-plus-generator  https://github.com/xiweile
 */
package com.inngke.ai.crm.db.crm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-28
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class JianyingResourceScene implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 雪花
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 关键词类型，详见：VideoKeywordTypeEnum
     */
    private String keywordType;

    /**
     * 类型 1:字幕 2:大字报
     */
    private Integer type;

    /**
     * 关联jianying_resource，json结构
     */
    private String resourceConfig;

    /**
     * 状态：-1=停用 1=正常使用
     */
    private Integer status;

    /**
     * 字幕风格，即 category.code (type=subtitle_style)
     */
    private Integer style;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;


    public static final String ID = "id";

    public static final String TITLE = "title";

    public static final String KEYWORD_TYPE = "keyword_type";

    public static final String TYPE = "type";

    public static final String RESOURCE_CONFIG = "resource_config";

    public static final String STATUS = "status";

    public static final String STYLE = "style";

    public static final String UPDATE_TIME = "update_time";

    public static final String CREATE_TIME = "create_time";

}
