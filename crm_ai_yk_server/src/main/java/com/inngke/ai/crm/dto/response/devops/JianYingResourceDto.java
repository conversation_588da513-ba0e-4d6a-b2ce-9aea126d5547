package com.inngke.ai.crm.dto.response.devops;

import com.inngke.ai.dto.config.JyResourceSceneConfig;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class JianYingResourceDto implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 关键词类型，详见：VideoKeywordTypeEnum
     */
    private String keywordType;

    /**
     * 风格
     */
    private Integer style;

    /**
     * 类型 1:字幕 2:大字报
     */
    private Integer type;

    /**
     * 关联jianying_resource，json结构
     */
    private String resourceSceneConfig;
}
