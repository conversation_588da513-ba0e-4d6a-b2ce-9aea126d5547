package com.inngke.ai.crm.dto.request.video;

import com.inngke.ai.crm.dto.response.ai.AiGenerateRequest;
import com.inngke.ai.dto.SubtitleDto;
import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ai.dto.request.BigTitleConfig;
import com.inngke.ai.dto.request.VideoDigitalHumanConfig;
import com.inngke.ai.dto.widget.WidgetGroup;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

@Data
@Accessors(chain = true)
public class VideoCreateWithMaterialRequest extends AiGenerateRequest {
    /**
     * 创作（草稿）ID`
     */
    private Long draftId;

    /**
     * 表单数据
     */
    private Map<String, Object> promptMap;

    /**
     * 操作步骤：10=通过视频关键信息生成视频内容 11=跳过 12=通过用户脚本生成视频内容 20=素材匹配 100=完成视频创作
     *
     * @demo 10
     */
    private Integer stage;

    /**
     * 创作任务id
     *
     * @demo 123456765432
     */
    private Long creationTaskId;
    /**
     * 视频脚本
     */
    private List<VideoUserScriptDto> scripts;

    /**
     * 字幕
     */
    private List<SubtitleDto> subtitles;

    /**
     * 通过此旧的taskId重新创作的，创作成功后，会删除旧的taskId记录
     *
     * @demo 3353232323111
     */
    private Long retryTaskId;

    /**
     * 视频分类ID，0=默认
     *
     * @demo 12345623456
     */
    private Long videoCategoryId;

    /**
     * 数字人配置，按列表顺序分别为：A、B、C...角色
     * 如果此值为空时，会尝试从 promptMap 构建 （匹配小程序场景）
     * PC端时，直接使用此值，忽略 promptMap
     */
    private List<VideoDigitalHumanConfig> digitalHumanConfigs;

    /**
     * 旁白角色
     *
     * @demo ["店员", "顾客"]
     */
    private List<String> roles;

    /**
     * 前贴视频
     * @deprecated beforeScript.materialList[]
     */
    private VideoMaterialItem beforeVideo;

    /**
     * 后贴视频
     * @deprecated 请使用afterScript.materialList[]
     */
    private VideoMaterialItem afterVideo;

    /**
     * 后帖分镜，分镜素材：afterScript.materialList[]
     * 单个工程可能有0~1个素材
     * 裂变工程可能会有0~n个素材
     */
    private VideoUserScriptDto afterScript;

    /**
     * 前帖分镜，分镜素材：beforeScript.materialList[]
     * 单个工程可能有0~1个素材
     * 裂变工程可能会有0~n个素材
     */
    private VideoUserScriptDto beforeScript;

    /**
     * pc,mp
     */
    private String source;

    /**
     * 不重复率，单位：百分比。30表示 30%不重复
     *
     * @demo 30
     */
    private Integer noDuplicatePercent;

    /**
     * 生成混剪视频数量
     */
    private Integer count;

    /**
     * 混剪类型，1：顺序，2：随机
     */
    private Integer mashUpType;

    /**
     * 前贴视频
     * @deprecated beforeScript.materialList[]
     */
    private List<VideoMaterialItem> beforeVideoList;

    /**
     * 后贴视频
     * @deprecated afterScript.materialList[]
     */
    private List<VideoMaterialItem> afterVideoList;

    /**
     * 已选的数字人列表
     */
    private List<VideoDigitalHumanConfig> chooseDigitalHumanConfigs;

    /**
     * 全局贴片配置，正常情况只有0~1个元素。在裂变时才会有多个
     */
    private List<WidgetGroup> widgets;

    /**
     * 新版大字报
     */
    private BigTitleConfig bigTitleConfig;
}
