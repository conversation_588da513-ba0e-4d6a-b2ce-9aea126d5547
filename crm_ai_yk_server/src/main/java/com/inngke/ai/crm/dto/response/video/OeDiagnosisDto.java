package com.inngke.ai.crm.dto.response.video;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class OeDiagnosisDto implements Serializable {
    /**
     * 状态： -2=检测任务失败 -1=提交检测失败 0=未检测 1=已提交检测 2=检测完成
     */
    private Integer status;

    /**
     * 是否AD优质素材
     */
    private Boolean isAdHighQualityMaterial;

    /**
     * 是否千川优质素材
     */
    private Boolean isEcpHighQualityMaterial;

    /**
     * 是否首发
     */
    private Boolean isFirstPublishMaterial;

    /**
     * 是否低效
     */
    private Boolean isInefficientMaterial;

    /**
     * 非AD优质素材的原因
     */
    private String notAdHighQualityReason;

    /**
     * 非千川优质素材的原因
     */
    private String notEcpHighQualityReason;

    /**
     * 预审状态-3=预审结果超时 -2=预审不通过 -1=提交失败 0=未提交 1=已提交 2=预审通过
     */
    private Integer auditStatus;

    /**
     * 预审拒绝理由
     */
    private String auditRejectReason;

    public static Boolean getValue(String value) {
        if (value == null || "UNKNOWN".equalsIgnoreCase(value)) {
            return null;
        }
        return "YES".equalsIgnoreCase(value);
    }

}
