package com.inngke.ai.crm.controller;

import com.inngke.ai.crm.dto.request.ContentModerationRequest;
import com.inngke.ai.crm.dto.request.common.AiFormRequest;
import com.inngke.ai.crm.dto.response.CrmPcConfigDto;
import com.inngke.ip.ai.dify.dto.DifyAppConfig;
import com.inngke.ai.crm.dto.response.ShortLinkDto;
import com.inngke.ai.crm.dto.response.common.PcFormConfigDto;
import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;
import com.inngke.ai.crm.service.*;
import com.inngke.ai.crm.service.impl.CommonService;
import com.inngke.common.core.config.sign.ApiSignature;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.ip.common.dto.request.GenMpQrRequest;
import com.inngke.ip.common.dto.response.UploadTempCredentialDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @chapter 公共接口
 * @section 获取客户端配置
 */
@RestController
@RequestMapping("/api/ai/common")
public class CommonApiController {
    public static final Integer TYPE_IMAGE = 1;
    public static final Integer TYPE_TEXT = 2;

    @Resource
    private CommonAuthService commonAuthService;

    @Autowired
    private CrmLoginService crmLoginService;

    @Autowired
    private DifyAppConfService difyAppConfService;

    @Autowired
    private ContentModeration contentModeration;

    @Autowired
    private CommonService commonService;

    @Autowired
    private FormConfigService formConfigService;


    @GetMapping("pc-config")
    public BaseResponse<CrmPcConfigDto> pcConfig() {
        return commonAuthService.pcConfig();
    }

    /**
     * 生成小程序二维码
     */
    @PostMapping("qr-code")
    public BaseResponse<String> genQrCode(@RequestBody GenMpQrRequest request) {
        return commonAuthService.genQrCode(request);
    }

    /**
     * 获取二维码的参数
     */
    @GetMapping("/scene/{id:\\d+}")
    public BaseResponse<String> getScene(@PathVariable("id") Long id) {
        return crmLoginService.getScene(id);
    }

    /**
     * 获取表单配置
     */
    @GetMapping("/formConfig")
    public BaseResponse<ProAiArticleTemplateDto> getProductFormConfigForPc(
            @RequestAttribute JwtPayload jwtPayload,
            AiFormRequest request
    ) {
        request.setUserId(jwtPayload.getCid());
        return BaseResponse.success(difyAppConfService.getForm(request));
    }

    /**
     * 获取PC端表单配置
     */
    @GetMapping("/formConfig/pc")
    public BaseResponse<PcFormConfigDto> getPcFormConfig(@RequestAttribute JwtPayload jwtPayload) {
        return BaseResponse.success(formConfigService.getPcFormConfig(jwtPayload));
    }

    /**
     * 内容审计
     */
    @PostMapping("/content/moderation")
    public BaseResponse<Boolean> contentModeration(ContentModerationRequest request) {
        if (TYPE_IMAGE.equals(request.getType())) {
            return BaseResponse.success(contentModeration.imageModeration(request.getTaskId(), request.getContent()));
        }

        if (TYPE_TEXT.equals(request.getType())) {
            return BaseResponse.success(contentModeration.textModeration(request.getTaskId(), request.getContent()));
        }

        return BaseResponse.success(true);
    }

    /**
     * 通过短链获取重定向地址
     */
    @GetMapping("/{code}/redirect")
    public BaseResponse<ShortLinkDto> shortCodeRedirect(@PathVariable String code) {
        return BaseResponse.success(commonService.shortCodeRedirect(code));
    }

    /**
     * 获取上传临时凭证
     */
    @GetMapping("/upload/credential")
    public BaseResponse<UploadTempCredentialDto> getUploadTempCredential(){
        return BaseResponse.success(commonService.getUploadTempCredential());
    }

    /**
     * 获取dify应用配置
     */
    @ApiSignature(signature = false)
    @GetMapping("/app-config")
    public BaseResponse<List<DifyAppConfig>> getAppConfigs() {
        return BaseResponse.success(difyAppConfService.getAppConfigs());
    }
}
