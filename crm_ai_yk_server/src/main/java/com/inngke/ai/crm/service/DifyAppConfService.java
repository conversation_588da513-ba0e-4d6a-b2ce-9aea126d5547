package com.inngke.ai.crm.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.inngke.ai.crm.db.crm.entity.DifyAppConf;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.DifyAppConfManager;
import com.inngke.ai.crm.db.crm.manager.UserManager;
import com.inngke.ai.crm.dto.enums.AiProductIdEnum;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.form.CommonFormConfig;
import com.inngke.ai.crm.dto.form.FormKeyItem;
import com.inngke.ai.crm.dto.request.common.AiFormRequest;
import com.inngke.ip.ai.dify.dto.DifyAppConfig;
import com.inngke.ai.crm.dto.response.ai.SimpleDifyAppConfDto;
import com.inngke.ai.crm.dto.response.pro.DifyAppConfDto;
import com.inngke.ai.crm.dto.response.pro.ProAiArticleTemplateDto;
import com.inngke.ai.crm.service.form.DynamicFormHandler;
import com.inngke.ai.crm.service.form.FormCodeHandler;
import com.inngke.ai.crm.service.form.handle.VideoHandler2;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import com.inngke.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.io.Serializable;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class DifyAppConfService {
    private static final Logger logger = LoggerFactory.getLogger(DifyAppConfService.class);

    @Autowired
    private DifyAppConfManager difyAppConfManager;

    @Autowired
    private UserManager userManager;

    @Autowired
    private StaffService staffService;

    @Autowired
    private AppConfigManager appConfigManager;

    @Autowired
    private JsonService jsonService;

    @Autowired
    private ProductService productService;

    @Autowired
    private List<FormCodeHandler> formCodeHandlerList;

    private Map<String, FormCodeHandler> formCodeHandlerMap;

    @Autowired
    private List<DynamicFormHandler> dynamicFormHandlerList;
    private Map<String, DynamicFormHandler> dynamicFormHandlerMap;

    @PostConstruct
    public void init() {
        formCodeHandlerMap = formCodeHandlerList.stream().collect(Collectors.toMap(FormCodeHandler::getFormCode, Function.identity()));
        dynamicFormHandlerMap = dynamicFormHandlerList.stream().collect(Collectors.toMap(DynamicFormHandler::getFormKey, Function.identity()));
    }

    private void dynamicFormProcess(ProAiArticleTemplateDto proAiArticleTemplateDto, long organizeId) {
        //运行过的handler
        Set<DynamicFormHandler> runHandlers = Sets.newHashSet();
        try {
            List<CommonFormConfig> config = proAiArticleTemplateDto.getFormColumnConfig();
            runHandlers.addAll(dynamicForm(organizeId, config, proAiArticleTemplateDto));
            Map<Integer, DifyAppConf> difyAppConfMap = difyAppConfManager.getMapByIds(
                    proAiArticleTemplateDto.getFormColumnConfigMap().keySet().stream().map(Integer::valueOf).collect(Collectors.toList())
            );

            proAiArticleTemplateDto.getFormColumnConfigMap().forEach((difyAppId, formConfig) ->
                    runHandlers.addAll(dynamicForm(organizeId, difyAppConfMap.get(Integer.valueOf(difyAppId)), formConfig, proAiArticleTemplateDto))
            );
        } finally {
            //清理handler
            runHandlers.forEach(DynamicFormHandler::clear);
        }
    }

    private Set<DynamicFormHandler> dynamicForm(long organizeId, List<CommonFormConfig> formConfigItems, ProAiArticleTemplateDto proAiArticleTemplateDto) {
        return dynamicForm(organizeId, null, formConfigItems, proAiArticleTemplateDto);
    }

    private Set<DynamicFormHandler> dynamicForm(long organizeId, DifyAppConf difyAppConf, List<CommonFormConfig> formConfigItems, ProAiArticleTemplateDto proAiArticleTemplateDto) {
        Set<String> deleteFormKeys = Sets.newHashSet();
        Set<DynamicFormHandler> dynamicFormHandlers = Sets.newHashSet();
        formConfigItems.forEach(formItem -> {
            String key = formItem.getKey();
            Serializable formKeyValue = Optional.ofNullable(formItem.getFromKeys()).map(Collection::stream).flatMap(Stream::findFirst).map(FormKeyItem::getValue).orElse(null);
//            if (Objects.nonNull(formKeyValue)) {
//                key = key + formKeyValue;
//            }
            Optional.ofNullable(dynamicFormHandlerMap.get(key)).ifPresent(dynamicFormHandler -> {
                dynamicFormHandlers.add(dynamicFormHandler);
                if (!dynamicFormHandler.handle(organizeId, difyAppConf, formItem, proAiArticleTemplateDto, String.valueOf(formKeyValue))) {
                    deleteFormKeys.add(formItem.getKey());
                }
            });
        });
        deleteFormKeys.forEach(key -> formConfigItems.removeIf(item -> key.equals(item.getKey())));
        return dynamicFormHandlers;
    }

    public static AppConfigCodeEnum getAppConfigCodeByProductId(Integer productId) {
        if (AiProductIdEnum.XIAO_HOME_SHU.getType().equals(productId)) {
            return AppConfigCodeEnum.PRO_AI_PROMPT_FORM_CONFIG;
        }
        if (AiProductIdEnum.VIDEO_CROP_MATERIAL.getType().equals(productId)) {
            return AppConfigCodeEnum.AI_VIDEO_FORM_CONFIG;
        }

        return null;
    }

    private DifyAppConfDto toDifyAppConfDto(DifyAppConf difyAppConf) {
        DifyAppConfDto difyAppConfDto = new DifyAppConfDto();
        difyAppConfDto.setValue(difyAppConf.getId());
        difyAppConfDto.setTitle(difyAppConf.getName());
        difyAppConfDto.setVipProduct(difyAppConf.getVipProduct());
        return difyAppConfDto;
    }

    public Map<Integer, DifyAppConfDto> getDifyAppConfMapByByProductId(Integer productId) {
        return difyAppConfManager.lambdaQuery()
                .eq(DifyAppConf::getAiProductId, productId).list()
                .stream().collect(Collectors.toMap(DifyAppConf::getId, this::toDifyAppConfDto));
    }

    /**
     * 获取表单配置
     */
    public ProAiArticleTemplateDto getForm(AiFormRequest request) {
        long organizeId = staffService.getUserStaffOrganizeId(request.getUserId());
        FormCodeHandler formHandler = formCodeHandlerMap.get(request.getFormCode());

        ProAiArticleTemplateDto videoConfig = getAigcConfig(organizeId, formHandler);

        dynamicFormProcess(videoConfig, organizeId);
        return videoConfig;
    }

    public ProAiArticleTemplateDto getAigcConfig(long organizeId, FormCodeHandler formHandler) {
        ProAiArticleTemplateDto proAiArticleTemplateDto = new ProAiArticleTemplateDto();

        //获取默认表单配置
        Integer aiProductType = formHandler.getApiProductEnum().getType();
        String formConfigCode = Optional.ofNullable(getAppConfigCodeByProductId(aiProductType))
                .map(AppConfigCodeEnum::getCode).orElse("none-no-config") +
                InngkeAppConst.DOT_STR + formHandler.getFormCode();
        //获取企业默认表单配置
        String organizeFormConfigCode = formConfigCode + InngkeAppConst.DOT_STR + organizeId;

        Map<String, String> defaultOrganizeConfigMap = appConfigManager.getValueByCodeList(
                Lists.newArrayList(
                        formConfigCode,
                        organizeFormConfigCode,
                        AppConfigCodeEnum.DIFY_APP_VIDEO_FORM_CONFIG1.getCode(),
                        AppConfigCodeEnum.DIFY_APP_VIDEO_FORM_CONFIG2.getCode(),
                        AppConfigCodeEnum.AI_VIDEO_FORM_PC_EXCLUDE_KEYS.getCode()
                )
        );
        //优先使用企业默认表单
        proAiArticleTemplateDto.setFormColumnConfig(jsonService.toObjectList(
                Optional.ofNullable(defaultOrganizeConfigMap.get(organizeFormConfigCode))
                        .orElse(defaultOrganizeConfigMap.get(formConfigCode)), CommonFormConfig.class
        ));

        //获取企业dify应用
        Map<Long, List<DifyAppConf>> organizeConfigTypeGroup = difyAppConfManager.organizeIdGroupMap(organizeId, aiProductType);

        List<DifyAppConf> appConfigList = Optional.ofNullable(organizeConfigTypeGroup.get(organizeId))
                .orElse(Optional.ofNullable(organizeConfigTypeGroup.get(0L)).orElse(Lists.newArrayList()));


        //删除掉不需要的表单
        String excludeKeyStr = defaultOrganizeConfigMap.getOrDefault(AppConfigCodeEnum.AI_VIDEO_FORM_PC_EXCLUDE_KEYS.getCode(), InngkeAppConst.EMPTY_STR);
        Set<String> excludeKeys = Arrays.stream(excludeKeyStr.split(InngkeAppConst.COMMA_STR))
                .map(key -> key.trim())
                .filter(key -> !org.springframework.util.StringUtils.isEmpty(key))
                .collect(Collectors.toSet());

        //如果未设置时，使用默认表单补偿
        String defaultVideoFormConfig2 = defaultOrganizeConfigMap.get(AppConfigCodeEnum.DIFY_APP_VIDEO_FORM_CONFIG2.getCode());
        String defaultVideoFormConfig1 = defaultOrganizeConfigMap.get(AppConfigCodeEnum.DIFY_APP_VIDEO_FORM_CONFIG1.getCode());
        appConfigList.forEach(appConfig -> {
            if (StringUtils.isEmpty(appConfig.getFormColumnConfig())) {
                appConfig.setFormColumnConfig(defaultVideoFormConfig1);
            }
            if (StringUtils.isEmpty(appConfig.getFormColumnConfig2())) {
                appConfig.setFormColumnConfig2(defaultVideoFormConfig2);
            }
        });

        //应用列表
        proAiArticleTemplateDto.setArticleTypeList(
                appConfigList.stream().filter(item -> Boolean.TRUE.equals(item.getVipProduct()))
                        .map(this::toDifyAppConfDto).collect(Collectors.toList())
        );

        Map<String, List<CommonFormConfig>> appMap = Maps.newHashMap();
        appConfigList.forEach(difyAppConf -> {
            List<CommonFormConfig> formConfigList = formHandler.getFormConfigs(difyAppConf, excludeKeys);
            appMap.put(difyAppConf.getId().toString(), formConfigList);
        });

        //应用表单配置
        proAiArticleTemplateDto.setFormColumnConfigMap(appMap);

        formHandler.afterHandle(organizeId, proAiArticleTemplateDto);

        return proAiArticleTemplateDto;
    }

    /**
     * VideoHandler2.FORM_CODE(视频生成的第二页表单)特殊处理取,difyAppConfig.formColumnConfig2
     */
    private Map<String, List<CommonFormConfig>> toFormColumnConfigMap(List<DifyAppConf> appConfigList, String formCode) {
        return appConfigList.stream().filter(conf -> StringUtils.isNotBlank(
                VideoHandler2.FORM_CODE.equals(formCode) ? conf.getFormColumnConfig2() : conf.getFormColumnConfig()
        )).collect(Collectors.toMap(conf -> conf.getId().toString(), conf -> {
            String formColumnConfig = conf.getFormColumnConfig();
            if (VideoHandler2.FORM_CODE.equals(formCode)) {
                formColumnConfig = conf.getFormColumnConfig2();
            }
            try {
                return jsonService.toObjectList(formColumnConfig, CommonFormConfig.class);
            } catch (Exception e) {
                logger.info("反序列化表单配置失败:{},data:{}", e.getMessage(), conf.getFormColumnConfig());
                return Lists.newArrayList();
            }
        }));
    }

    /**
     * 获取视频应用配置，如果未设置会自动使用默认配置补全
     *
     * @param appId 应用ID
     */
    public DifyAppConf getVideoAppConf(int appId) {
        DifyAppConf videoAppConf = difyAppConfManager.getById(appId);
        if (videoAppConf == null) {
            return null;
        }
        Set<String> defaultConfKeys = Sets.newHashSet();
        if (org.springframework.util.StringUtils.isEmpty(videoAppConf.getFormColumnConfig())) {
            defaultConfKeys.add(AppConfigCodeEnum.DIFY_APP_VIDEO_FORM_CONFIG1.getCode());
        }
        if (org.springframework.util.StringUtils.isEmpty(videoAppConf.getFormColumnConfig2())) {
            defaultConfKeys.add(AppConfigCodeEnum.DIFY_APP_VIDEO_FORM_CONFIG2.getCode());
        }
        if (defaultConfKeys.isEmpty()) {
            return videoAppConf;
        }

        Map<String, String> defaultConfigMap = appConfigManager.getValueByCodeList(
                Lists.newArrayList(
                        AppConfigCodeEnum.DIFY_APP_VIDEO_FORM_CONFIG1.getCode(),
                        AppConfigCodeEnum.DIFY_APP_VIDEO_FORM_CONFIG2.getCode()
                )
        );
        if (org.springframework.util.StringUtils.isEmpty(videoAppConf.getFormColumnConfig())) {
            videoAppConf.setFormColumnConfig(defaultConfigMap.get(AppConfigCodeEnum.DIFY_APP_VIDEO_FORM_CONFIG1.getCode()));
        }
        if (org.springframework.util.StringUtils.isEmpty(videoAppConf.getFormColumnConfig2())) {
            videoAppConf.setFormColumnConfig2(defaultConfigMap.get(AppConfigCodeEnum.DIFY_APP_VIDEO_FORM_CONFIG2.getCode()));
        }
        return videoAppConf;
    }

    private List<DifyAppConf> overwriteIfPresent(Map<Long, List<DifyAppConf>> organizeConfigTypeGroup, long organizeId) {
        List<DifyAppConf> defaultDifyAppList = organizeConfigTypeGroup.get(0L);
        List<DifyAppConf> organizeDifyAppList = organizeConfigTypeGroup.get(organizeId);
        if (CollectionUtils.isEmpty(organizeDifyAppList)) {
            return defaultDifyAppList;
        }

        Set<Integer> coverIds = organizeDifyAppList.stream().map(DifyAppConf::getCoverId).filter(Objects::nonNull).collect(Collectors.toSet());

        List<DifyAppConf> res = defaultDifyAppList.stream().filter(defaultDifyApp -> !coverIds.contains(defaultDifyApp.getId())).collect(Collectors.toList());
        res.addAll(organizeDifyAppList);

        res.sort(Comparator.comparing(DifyAppConf::getSort));

        return res;
    }

    public BaseResponse<List<SimpleDifyAppConfDto>> getDifyAppList(JwtPayload jwtPayload, Integer aiProductId) {
        Long userOrganizeId = userManager.getUserOrganizeId(jwtPayload.getCid());
        if (Objects.isNull(userOrganizeId) || Objects.isNull(aiProductId)) {
            return BaseResponse.success(Lists.newArrayList());
        }

        return BaseResponse.success(
                difyAppConfManager.list(Wrappers.<DifyAppConf>query()
                        .eq(DifyAppConf.ORGANIZE_ID, userOrganizeId)
                        .eq(DifyAppConf.AI_PRODUCT_ID, aiProductId)
                ).stream().map(difyAppConf -> {
                    SimpleDifyAppConfDto simpleDifyAppConfDto = new SimpleDifyAppConfDto();
                    simpleDifyAppConfDto.setId(difyAppConf.getId());
                    simpleDifyAppConfDto.setName(difyAppConf.getName());
                    return simpleDifyAppConfDto;
                }).collect(Collectors.toList())
        );
    }

    public List<DifyAppConfig> getAppConfigs() {
        String difyConfigJson = appConfigManager.getValueByCode("video.dify.config");
        if (!org.springframework.util.StringUtils.hasLength(difyConfigJson)) {
            throw new InngkeServiceException("未配置 app_config[code=video.dify.config]");
        }
        return JsonUtil.jsonToList(difyConfigJson, DifyAppConfig.class);
    }
}
