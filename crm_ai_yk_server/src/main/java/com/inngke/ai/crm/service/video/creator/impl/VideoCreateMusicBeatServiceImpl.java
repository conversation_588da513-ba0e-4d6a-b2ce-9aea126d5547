package com.inngke.ai.crm.service.video.creator.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.inngke.ai.crm.client.video.VideoCreateServiceForCrm;
import com.inngke.ai.crm.core.util.CrmUtils;
import com.inngke.ai.crm.core.util.RetryTaskUtils;
import com.inngke.ai.crm.core.util.VideoScriptUtils;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;
import com.inngke.ai.crm.db.crm.entity.VideoProjectDraft;
import com.inngke.ai.crm.db.crm.manager.AppConfigManager;
import com.inngke.ai.crm.db.crm.manager.MashUpTaskManager;
import com.inngke.ai.crm.db.crm.manager.VideoBgmMaterialManager;
import com.inngke.ai.crm.db.crm.manager.VideoProjectDraftManager;
import com.inngke.ai.crm.dto.enums.AiGenerateTaskStatusEnum;
import com.inngke.ai.crm.dto.enums.AppConfigCodeEnum;
import com.inngke.ai.crm.dto.enums.VideoCreationStageEnum;
import com.inngke.ai.crm.dto.enums.VideoDraftTypeEnum;
import com.inngke.ai.crm.dto.form.SelectOption;
import com.inngke.ai.crm.dto.request.video.VideoCreateWithMaterialRequest;
import com.inngke.ai.crm.dto.request.video.VideoMashUpCreateRequest;
import com.inngke.ai.crm.dto.response.video.VideoCreateResult;
import com.inngke.ai.crm.dto.response.video.VideoProjectDraftDetail;
import com.inngke.ai.crm.service.DifyService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.ai.crm.service.VideoCreateService;
import com.inngke.ai.crm.service.VideoProjectDraftService;
import com.inngke.ai.crm.service.video.creator.VideoCreatorService;
import com.inngke.ai.dto.BaseVideoMaterial;
import com.inngke.ai.dto.VideoMaterialItem;
import com.inngke.ai.dto.VideoProject;
import com.inngke.ai.dto.VideoUserScriptDto;
import com.inngke.ai.dto.enums.VideoTypeEnum;
import com.inngke.ai.dto.request.VideoBgmConfig;
import com.inngke.ai.dto.request.VideoGenerateRequest;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.ai.dto.widget.WidgetGroup;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.dify.app.VideoScriptSceneV2App;
import com.inngke.ip.ai.dify.app.dto.VideoScriptSceneResp;
import com.inngke.ip.ai.dify.utils.DifyRequestInputsBuilder;
import com.inngke.ip.ai.volc.dto.response.BeatTrackingResult;
import com.inngke.ip.ai.volc.service.VolcSamiService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.inngke.ai.crm.service.impl.BgmServiceImpl.MY_MUSIC_TYPE;
import static com.inngke.ai.crm.service.impl.VideoMaterialMashUpServiceImpl.MASH_UP_TYPE_RANDOM;

@Service
public class VideoCreateMusicBeatServiceImpl implements VideoCreatorService {

    private static final Logger logger = LoggerFactory.getLogger(VideoCreateMusicBeatServiceImpl.class);

    @Autowired
    private StaffService staffService;
    @Autowired
    private VideoProjectDraftManager videoProjectDraftManager;
    @Autowired
    private VolcSamiService volcSamiService;
    @Autowired
    private VideoBgmMaterialManager videoBgmMaterialManager;
    @Autowired
    private VideoScriptSceneV2App videoScriptSceneV2App;
    @Autowired
    private VideoCreateServiceForCrm videoCreateServiceForCrm;
    @Autowired
    private VideoCreateService videoCreateService;
    @Autowired
    private VideoProjectDraftService videoProjectDraftService;
    @Value("${server.url:}")
    private String aiServer;
    @Autowired
    private DifyService difyService;
    @Autowired
    private MashUpTaskManager mashUpTaskManager;


    @Override
    public List<VideoDraftTypeEnum> getCreateType() {
        return Lists.newArrayList(VideoDraftTypeEnum.MUSIC_BEAT);
    }

    @Override
    public VideoProjectDraftDetail genDraftScript(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request) {
        Long userId = jwtPayload.getCid();
        Staff staff = staffService.getStaffByUserId(userId);
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        request.getPromptMap().put(FormDataUtils.FORM_KEY_VIDEO_TYPE, VideoTypeEnum.MUSIC_BEAT_VIDEO.getVideoType());
        Optional.ofNullable(FormDataUtils.getString(request.getPromptMap(), FormDataUtils.FORM_KEY_VIDEO_THEME))
                .ifPresent(them -> request.getPromptMap().put(FormDataUtils.FORM_KEY_VIDEO_TITLE, them));


        Long draftId = Optional.ofNullable(request.getDraftId()).orElse(SnowflakeHelper.getId());

        // 生成音乐踩点脚本
        List<VideoUserScriptDto> scripts = generateMusicBeatScripts(request, staff, draftId);
        request.setScripts(scripts);
        request.setDraftId(draftId);
        request.setTaskId(draftId);

        // 保存草稿
        VideoProjectDraft draft = this.initMusicBeatProjectDraft(staff, draftId, request);

        //获取第一个分镜的素材做为封面
        scripts.stream().findFirst().map(VideoUserScriptDto::getMaterialList).orElse(Lists.newArrayList())
                .stream().findFirst().map(BaseVideoMaterial::getUrl).ifPresent(
                        url -> draft.setCoverImage(url + "?x-oss-process=video/snapshot,t_0,f_jpg,m_fast")
                );


        VideoProjectDraft exist = videoProjectDraftManager.getById(draftId);
        if (exist != null) {
            videoProjectDraftManager.updateById(draft);
        } else {
            draft.setCreateTime(LocalDateTime.now());
            videoProjectDraftManager.save(draft);
            videoProjectDraftService.saveUserVideoConfig(request.getPromptMap(), staff.getUserId(), null);
        }

        return videoProjectDraftService.getDraftDetail(jwtPayload, draftId);
    }

    @Override
    public VideoCreateResult createVideo(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request) {
        Long userId = jwtPayload.getCid();
        Staff staff = staffService.getStaffByUserId(userId);

        String draftTitle = FormDataUtils.getString(request.getPromptMap(), FormDataUtils.FORM_KEY_VIDEO_THEME);
        draftTitle = StringUtils.isNotBlank(draftTitle) ? draftTitle : null;
        videoProjectDraftService.saveDraft(jwtPayload, request, draftTitle);
        request.getPromptMap().put(FormDataUtils.FORM_KEY_VIDEO_TYPE, VideoTypeEnum.MUSIC_BEAT_VIDEO.getVideoType());

        int miniMaterialCount = request.getScripts().stream().map(script ->
                org.apache.dubbo.common.utils.CollectionUtils.size(script.getMaterialOriginList())
        ).min(Integer::compareTo).orElse(0);

        if (miniMaterialCount == 0) {
            throw new InngkeServiceException("生成失败：存在素材为空的分镜");
        }

        //将视频任务添加到 video_create_task
        List<Long> mashUpTaskIds = Lists.newArrayList();
        if (request.getCount() < miniMaterialCount) {
            miniMaterialCount = request.getCount();
        }

        //获取背景音乐
        List<Long> musicIds = getChooseMusicIds(request.getPromptMap());
        List<VideoBgmMaterial> musicList;
        if (!CollectionUtils.isEmpty(musicIds)) {
            musicList = Lists.newArrayList(videoBgmMaterialManager.listByIds(musicIds));
        } else {
            int bgmType = FormDataUtils.getInt(request.getPromptMap(), FormDataUtils.FORM_KEY_BGM_TYPE, 1);
            musicList = videoBgmMaterialManager.list(
                    Wrappers.<VideoBgmMaterial>query()
                            .in(VideoBgmMaterial.ORGANIZE_ID, Lists.newArrayList(0, staff.getOrganizeId()))
                            .eq(VideoBgmMaterial.TYPE, bgmType)
                            .eq(VideoBgmMaterial.USER_ID, bgmType == 127 ? staff.getUserId() : 0)
                            .eq(VideoBgmMaterial.STATUS, 1)
                            .last("limit " + miniMaterialCount + 1)
            );
            Collections.shuffle(musicList);
        }

        if (CollectionUtils.isEmpty(musicIds) && (CollectionUtils.isEmpty(musicList) || musicList.size() < miniMaterialCount)) {
            throw new InngkeServiceException("所选分类音乐不足，请重新选择音乐分类！");
        }

        //设置视频标题
        Optional.ofNullable(FormDataUtils.getString(request.getPromptMap(), FormDataUtils.FORM_KEY_VIDEO_THEME))
                .ifPresent(them -> request.getPromptMap().put(FormDataUtils.FORM_KEY_VIDEO_TITLE, them));

        // 随机合成
        if (MASH_UP_TYPE_RANDOM.equals(request.getMashUpType())) {
            request.getScripts().forEach(script -> {
                Collections.shuffle(script.getMaterialOriginList());
            });
        }

        for (int index = 0; index < miniMaterialCount; index++) {
            VideoCreateResult result = videoCreateService.createByMaterial(jwtPayload, genCreateRequest(request, staff, musicList, index), null, true);
            mashUpTaskIds.add(result.getTaskId());
        }

        boolean submitted = mashUpTaskManager.submitMashUpTask(mashUpTaskIds);

        VideoCreateResult result = new VideoCreateResult();
        result.setTaskId(request.getDraftId());
        result.setTaskStatus(AiGenerateTaskStatusEnum.INIT_CONFIG.getCode());
        result.setVideoUrl(null);
        result.setCreateStepInfo(VideoCreationStageEnum.FINISH_VIDEO.getDesc());
        return result;
    }

    @Override
    public VideoGenerateRequest buildVideoGenerateRequest(VideoCreateWithMaterialRequest request, Staff staff) {
        VideoGenerateRequest videoGenerateRequest = new VideoGenerateRequest();
        videoGenerateRequest.setFormQuery(request.getPromptMap());
        videoGenerateRequest.setScripts(request.getScripts());
        videoGenerateRequest.setBeforeScript(request.getBeforeScript());
        videoGenerateRequest.setAfterScript(request.getAfterScript());
        videoGenerateRequest.setDraftId(request.getDraftId());
        videoGenerateRequest.setTaskId(request.getTaskId());
        videoGenerateRequest.setUserId(staff.getUserId());
        videoGenerateRequest.setStage(request.getStage());
        videoGenerateRequest.setSubtitles(Lists.newArrayList());
        videoGenerateRequest.setAiServer(StringUtils.isNotBlank(aiServer) ? aiServer : CrmUtils.getAiServerUrl());
        videoGenerateRequest.setWidgets(request.getWidgets());
        long musicId = FormDataUtils.getLong(request.getPromptMap(), FormDataUtils.FORM_KEY_CHOOSE_MUSIC, 0);
        if (musicId == 0) {
            throw new InngkeServiceException("获取背景音乐失败");
        }

        VideoBgmMaterial bgmMaterial = videoBgmMaterialManager.getById(musicId);

        VideoBgmConfig bgm = new VideoBgmConfig();
        bgm.setId(bgmMaterial.getId());
        bgm.setUrl(bgmMaterial.getUrl());
        bgm.setVolume(getGbmVolume(bgmMaterial));
        bgm.setDuration(bgmMaterial.getDuration());
        bgm.setStart(bgmMaterial.getClipStart());

        videoGenerateRequest.setBgm(bgm);

        //新版大字报
        videoGenerateRequest.setBigTitleConfig(request.getBigTitleConfig());

        return videoGenerateRequest;
    }

    private double getGbmVolume(VideoBgmMaterial videoBgmMaterial) {
        if (videoBgmMaterial == null || videoBgmMaterial.getVolume() == 0) {
            return -8.0;
        }
        //统一调整到 -30dB
        return (-3000 - videoBgmMaterial.getVolume()) / 100.0;
    }

    private List<VideoUserScriptDto> generateMusicBeatScripts(VideoCreateWithMaterialRequest request, Staff staff, Long draftId) {
        Map<String, Object> promptMap = request.getPromptMap();

        // 1. 获取音乐URL
        VideoBgmMaterial music = getMusic(promptMap, staff);

        // 2. 进行节拍检测
        BeatTrackingResult beatResult = beatTracking(music);

        // 脚本
        List<VideoUserScriptDto> scripts = generateScriptsByDify(promptMap, staff);

        // 3. 是否使用AI转场
        boolean aiTransition = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_TRANSITION_TYPE, 0) == 1;
        if (!aiTransition) {
            scripts.forEach(script -> script.setTransitionId(0));
        }

        // 4. 根据节拍检测结果调整脚本时长
        adjustScriptDurationByBeat(scripts, beatResult, promptMap);

        // 5. 匹配视频素材
        return matchVideoMaterials(scripts, promptMap, staff, draftId);
    }

    private VideoCreateWithMaterialRequest genCreateRequest(VideoCreateWithMaterialRequest request, Staff staff, List<VideoBgmMaterial> musicList, int index) {
        VideoCreateWithMaterialRequest materialRequest = new VideoCreateWithMaterialRequest();
        materialRequest.setDraftId(request.getDraftId());
        materialRequest.setTaskId(SnowflakeHelper.getId());
        materialRequest.setPromptMap(request.getPromptMap());
        materialRequest.setStage(VideoCreationStageEnum.FINISH_VIDEO.getCode());
        materialRequest.setVideoCategoryId(request.getVideoCategoryId());

        // 1. 设置素材
        List<VideoUserScriptDto> newScripts = request.getScripts().stream().map(script -> {
            VideoUserScriptDto newScript = new VideoUserScriptDto();
            BeanUtils.copyProperties(script, newScript);
            newScript.setMaterialList(Lists.newArrayList(script.getMaterialOriginList().get(index)));
            newScript.setMaterialOriginList(script.getMaterialList());
            return newScript;
        }).collect(Collectors.toList());
        materialRequest.setScripts(newScripts);
        Long chooseMusicId = musicList.get(index % musicList.size()).getId();

        materialRequest.getPromptMap().put(FormDataUtils.FORM_KEY_CHOOSE_MUSIC, chooseMusicId);
        materialRequest.getPromptMap().put(FormDataUtils.FORM_KEY_CHOOSE_MUSIC_IDS, Lists.newArrayList(chooseMusicId));

        // 2. 进行节拍检测
        BeatTrackingResult beatResult = beatTracking(musicList.get(index % musicList.size()));

        // 3. 根据节拍检测结果调整脚本时长
        adjustScriptDurationByBeat(materialRequest.getScripts(), beatResult, request.getPromptMap());

        // 4.重新设置素材时长
        for (VideoUserScriptDto script : materialRequest.getScripts()) {
            VideoMaterialItem material = script.getMaterialList().get(0);
            // 以原素材开始时间加上脚本时长后 素材时长不够用
            if (material.getClipStart() + script.getDuration() > material.getDuration()) {
                // 素材自身时长够用 开始时间向前移动
                if (material.getDuration() > script.getDuration()) {
                    material.setClipStart(material.getDuration() - script.getDuration());
                } else {
                    material.setSpeed(80);
                    material.setClipDuration((int) (material.getDuration() * 0.8));
                }
            }

            material.setClipDuration(script.getDuration());
        }

        // 设置前贴视频
        setBeforeAfterScript(materialRequest, request, index);

        //设置贴片
        videoWidgets(materialRequest, request, index);

        return materialRequest;
    }

    private void setBeforeAfterScript(VideoCreateWithMaterialRequest mashUpRequest, VideoCreateWithMaterialRequest request, int index) {
        List<VideoMaterialItem> beforeMaterial = Optional.ofNullable(request.getBeforeScript()).map(VideoUserScriptDto::getMaterialList).orElse(Lists.newArrayList());
        if (!CollectionUtils.isEmpty(beforeMaterial)) {
            mashUpRequest.setBeforeScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList(beforeMaterial.get(index % beforeMaterial.size()))));
        }

        List<VideoMaterialItem> afterMaterial = Optional.ofNullable(request.getBeforeScript()).map(VideoUserScriptDto::getMaterialList).orElse(null);
        if (!CollectionUtils.isEmpty(afterMaterial)) {
            mashUpRequest.setAfterScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList(afterMaterial.get(index % afterMaterial.size()))));
        }
    }

    private void videoWidgets(VideoCreateWithMaterialRequest mashUpRequest, VideoCreateWithMaterialRequest request, int index) {
        List<WidgetGroup> widgets = request.getWidgets();
        if (CollectionUtils.isEmpty(widgets)) {
            return;
        }

        // 全局贴片
        WidgetGroup widgetGroup = widgets.get(index % widgets.size());
        mashUpRequest.setWidgets(Lists.newArrayList(widgetGroup));
    }

    private VideoBgmMaterial getMusic(Map<String, Object> promptMap, Staff staff) {
        List<Long> musicIds = getChooseMusicIds(promptMap);
        // 尝试从chooseMusicIds获取音乐ID
        if (!CollectionUtils.isEmpty(musicIds)) {
            // 使用用户选择的音乐
            Long musicId = musicIds.get(0);
            VideoBgmMaterial bgm = videoBgmMaterialManager.getById(musicId);
            if (bgm != null) {
                return bgm;
            }
        }

        // 通过bgmType随机获取音乐
        int bgmType = FormDataUtils.getInt(promptMap, "bgmType", 1);
        VideoBgmMaterial randomBgm = videoBgmMaterialManager.random(
                staff.getOrganizeId(), MY_MUSIC_TYPE == bgmType ? staff.getUserId() : 0, bgmType
        );
        if (randomBgm != null) {
            return randomBgm;
        }

        throw new InngkeServiceException("未找到可用的背景音乐");
    }

    private List<Long> getChooseMusicIds(Map<String, Object> promptMap) {
        List<Long> musicIds = Lists.newArrayList();
        Object chooseMusicIds = promptMap.get(FormDataUtils.FORM_KEY_CHOOSE_MUSIC_IDS);
        if (Objects.nonNull(chooseMusicIds)) {
            try {
                String chooseMusicIdsStr = JsonUtil.toJsonString(chooseMusicIds);
                musicIds = JsonUtil.jsonToList(chooseMusicIdsStr, Long.class);
            } catch (Exception e) {
                logger.info("解析音乐ids失败");
            }
        }

        return musicIds;
    }

    private List<VideoUserScriptDto> generateScriptsByDify(Map<String, Object> promptMap, Staff staff) {
        try {
            String videoTheme = FormDataUtils.getString(promptMap, FormDataUtils.FORM_KEY_VIDEO_THEME, "");
            int scriptCount = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_SCRIPT_COUNT, 5);
            double beat = Double.parseDouble(FormDataUtils.getString(promptMap, FormDataUtils.FORM_KEY_BEAT, "1.5"));

            // 构建Dify请求参数
            Map<String, String> inputs = DifyRequestInputsBuilder.newBuilder()
                    .set("form", "踩点")
                    .set("scout_topic", videoTheme)
                    .set("scout_step", beat)
                    .set("scout_num", scriptCount)
                    .build();

            String difyUserId = difyService.getUser(staff.getUserId());

            // 调用VideoScriptSceneApp工作流
            VideoScriptSceneResp scriptResp = RetryTaskUtils.process(o -> videoScriptSceneV2App.execute(difyUserId, inputs));

            if (scriptResp != null && !CollectionUtils.isEmpty(scriptResp.getScripts())) {
                return scriptResp.getScripts().stream()
                        .limit(scriptCount)
                        .collect(Collectors.toList());
            }

            // 如果Dify调用失败，生成默认脚本
            throw new InngkeServiceException("生成分镜脚本失败，请重试");

        } catch (Exception e) {
            logger.error("调用Dify生成脚本失败", e);
            throw new InngkeServiceException("生成分镜脚本失败，请重试");
        }
    }

    /**
     * 根据节拍检测结果调整脚本时长
     */
    private void adjustScriptDurationByBeat(List<VideoUserScriptDto> scripts, BeatTrackingResult beatResult, Map<String, Object> promptMap) {
        if (CollectionUtils.isEmpty(scripts) || beatResult == null || beatResult.getBpm() == null) {
            return;
        }

        double beatRhythm = Double.parseDouble(FormDataUtils.getString(promptMap, FormDataUtils.FORM_KEY_BEAT, "1.5"));

        //四舍五入
        int interval = (int) Math.round(beatResult.getBpm() / 60 * beatRhythm);

        int index = 0;
        List<BeatTrackingResult.Beat> beats = beatResult.getBeats();
        if (CollectionUtils.isEmpty(beats)) {
            throw new InngkeServiceException("音乐节拍检测失败，请重试");
        }

        VideoUserScriptDto preScript = null;
        for (VideoUserScriptDto script : scripts) {
            int nextIndex = index + 1;

            BeatTrackingResult.Beat beat = beats.get((interval * index) % beats.size());

            BeatTrackingResult.Beat nextBeat = beats.get((interval * nextIndex) % beats.size());
            // 触发音乐循环
            if (nextBeat.getTime() < beat.getTime()) {
                beat = nextBeat;
                nextBeat = beats.get((interval * (nextIndex + 1)) % beats.size());
            }

            script.setDuration((int) ((nextBeat.getTime() - beat.getTime()) * 1000));
            if (preScript != null) {
                script.setStartTime(preScript.getStartTime() + preScript.getDuration());
            } else {
                script.setStartTime(0);
            }

            script.setStart(VideoScriptUtils.toTimeMaxMinute(script.getStartTime()));
            script.setEnd(VideoScriptUtils.toTimeMaxMinute(script.getStartTime() + script.getDuration()));

            preScript = script;

            index++;
        }
    }

    /**
     * 生成默认脚本
     */
    private List<VideoUserScriptDto> generateDefaultScripts(int scriptCount) {
        List<VideoUserScriptDto> scripts = Lists.newArrayList();
        for (int i = 0; i < scriptCount; i++) {
            VideoUserScriptDto script = new VideoUserScriptDto();
            script.setScene("默认场景描述 " + (i + 1));
            script.setAside("默认旁白内容 " + (i + 1));
            script.setDuration(2000); // 默认2秒
            script.setMaterialList(Lists.newArrayList());
            script.setMaterialOriginList(Lists.newArrayList());
            scripts.add(script);
        }
        return scripts;
    }

    /**
     * 初始化音乐踩点项目草稿
     */
    private VideoProjectDraft initMusicBeatProjectDraft(Staff staff, Long draftId, VideoCreateWithMaterialRequest request) {
        VideoProjectDraft draft = new VideoProjectDraft();
        draft.setId(draftId);
        draft.setStaffId(staff.getId());
        draft.setOrganizeId(staff.getOrganizeId());
        draft.setType(VideoDraftTypeEnum.MUSIC_BEAT.getCode()); // 音乐踩点类型
        draft.setTitle(Optional.ofNullable(FormDataUtils.getString(request.getPromptMap(), FormDataUtils.FORM_KEY_VIDEO_THEME))
                .orElse("音乐踩点视频_" + draftId));
        draft.setProjectContext(JsonUtil.toJsonString(request));
        draft.setCreateType(0);
        draft.setDeleted(false);
        draft.setUpdateTime(LocalDateTime.now());

        return draft;
    }

    /**
     * 获取音乐踩点草稿详情
     */

    private BeatTrackingResult beatTracking(VideoBgmMaterial music) {
        if (StringUtils.isNotBlank(music.getBeatTracking())) {
            try {
                return JsonUtil.jsonToObject(music.getBeatTracking(), BeatTrackingResult.class);
            } catch (Exception e) {
                logger.info("节拍数据序列化失败");
            }
        }

        BeatTrackingResult beatTrackingResult = volcSamiService.beatTracking(music.getUrl());

        videoBgmMaterialManager.update(Wrappers.<VideoBgmMaterial>update()
                .eq(VideoBgmMaterial.ID, music.getId())
                .set(VideoBgmMaterial.BEAT_TRACKING, JsonUtil.toJsonString(beatTrackingResult))
        );

        int clipStart = music.getClipStart();
        int clipEnd = music.getDuration() + music.getClipStart();
        if (clipStart > 0 && clipEnd > 0) {
            List<BeatTrackingResult.Beat> beats = beatTrackingResult.getBeats();
            beatTrackingResult.setBeats(
                    beats.stream().filter(beat -> beat.getTime() * 1000 > clipStart && beat.getTime() * 1000 < clipEnd)
                            .collect(Collectors.toList())
            );
        }

        return beatTrackingResult;
    }

    /**
     * 匹配视频素材
     *
     * @return
     */
    private List<VideoUserScriptDto> matchVideoMaterials(List<VideoUserScriptDto> scripts, Map<String, Object> promptMap, Staff staff, Long draftId) {
        VideoGenerateRequest videoRequest = new VideoGenerateRequest();
        videoRequest.setScripts(scripts);
        videoRequest.setFormQuery(promptMap);
        videoRequest.setDraftId(draftId);
        videoRequest.setAiServer(aiServer);

        for (int i = 0; i < 3; i++) {
            try {
                VideoProject videoProject = videoCreateServiceForCrm.matchMaterialSync(videoRequest);

                List<VideoUserScriptDto> matchedMaterialScripts = videoProject.getScripts();

                if (CollectionUtils.isEmpty(matchedMaterialScripts)) {
                    throw new InngkeServiceException("匹配素材失败");
                }
                matchedMaterialScripts.forEach(script -> {
                    //取1个素材
                    script.setMaterialOriginList(script.getMaterialOriginList().stream().limit(1).collect(Collectors.toList()));
                });

                return matchedMaterialScripts;
            } catch (Exception e) {
                logger.info("匹配素材失败");
            }
        }

        throw new InngkeServiceException("匹配素材失败");
    }
}
