package com.inngke.ai.crm.service.material.task;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.api.video.VideoMaterialApi;
import com.inngke.ai.crm.api.video.dto.VideoOceanEngineDiagnosisRequest;
import com.inngke.ai.crm.api.video.dto.VideoOceanEngineDiagnosisResponse;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.db.crm.entity.AiGenerateVideoOutput;
import com.inngke.ai.crm.db.crm.entity.VideoOceanengineDiagnosis;
import com.inngke.ai.crm.db.crm.manager.AiGenerateVideoOutputManager;
import com.inngke.ai.crm.db.crm.manager.VideoOceanengineDiagnosisManager;
import com.inngke.ai.crm.dto.VideoMaterialTask;
import com.inngke.ai.crm.service.OceanEngineAccessTokenService;
import com.inngke.ai.crm.service.material.VideoMaterialTaskService;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.service.LockService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.JsonUtil;
import com.inngke.ip.ai.volc.api.OceanEngineVideoApi;
import com.inngke.ip.ai.volc.dto.request.OpenMaterialAuditRequest;
import com.inngke.ip.ai.volc.dto.response.BaseVolcResponse;
import com.inngke.ip.ai.volc.dto.response.OpenMaterialAuditResponse;
import com.inngke.ip.ai.volc.dto.response.VideoOceanEngineDiagnosisResultResponse;
import com.inngke.ip.ai.volc.dto.response.VideoOceanEngineDiagnosisTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component(VideoOceanEngineDiagnosisTaskHandler.TASK_TYPE)
public class VideoOceanEngineDiagnosisTaskHandler implements MaterialTaskHandler<Long, VideoOceanEngineDiagnosisResponse> {
    private static final Logger logger = LoggerFactory.getLogger(VideoOceanEngineDiagnosisTaskHandler.class);

    public static final String TASK_TYPE = "videoOceanEngineDiagnosis";

    public static final Long ADVERTISER_ID = 1829722283293193L;

    @Autowired
    private VideoMaterialApi videoMaterialApi;

    @Autowired
    private OceanEngineVideoApi oceanEngineVideoApi;

    @Autowired
    private LockService lockService;

    @Autowired
    private VideoOceanengineDiagnosisManager videoOceanengineDiagnosisManager;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private OceanEngineAccessTokenService oceanEngineAccessTokenService;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private VideoMaterialTaskService videoMaterialTaskService;

    @Override
    public String taskType() {
        return TASK_TYPE;
    }

    @Override
    public void afterSubmitTask(VideoMaterialTask<Long> task) {

    }

    @Override
    public VideoOceanEngineDiagnosisResponse process(VideoMaterialTask<Long> task) {
        videoOceanengineDiagnosisManager.update(
                Wrappers.<VideoOceanengineDiagnosis>update()
                        .eq(VideoOceanengineDiagnosis.ID, task.getId())
                        .set(VideoOceanengineDiagnosis.STATUS, 1)
                        .set(VideoOceanengineDiagnosis.OE_ADVERTISER_ID, ADVERTISER_ID)
                        .set(VideoOceanengineDiagnosis.COMMIT_TIME, LocalDateTime.now())
        );
        BaseResponse<VideoOceanEngineDiagnosisResponse> resp = videoMaterialApi.videoOceanEngineDiagnosis(new VideoOceanEngineDiagnosisRequest().setUrl(task.getUrl()));
        if (BaseResponse.responseSuccessWithNonNullData(resp)) {
            return resp.getData();
        }
        logger.error("提交视频首发任务失败: {}", JsonUtil.toJsonString(resp));
        return null;
    }

    @Override
    public boolean isTaskSuccess(VideoMaterialTask<Long> task, VideoOceanEngineDiagnosisResponse data) {
        return data != null && StringUtils.hasLength(data.getVideoId());
    }

    @Override
    public void callback(VideoMaterialTask<Long> task, VideoOceanEngineDiagnosisResponse data) {
        LocalDateTime now = LocalDateTime.now();
        if (isTaskSuccess(task, data)) {
            //成功
            videoOceanengineDiagnosisManager.update(
                    Wrappers.<VideoOceanengineDiagnosis>update()
                            .eq(VideoOceanengineDiagnosis.ID, task.getId())
                            .set(VideoOceanengineDiagnosis.STATUS, 1)
                            .set(VideoOceanengineDiagnosis.COMMIT_TIME, now)
                            .set(VideoOceanengineDiagnosis.OE_VIDEO_ID, data.getVideoId())
                            .set(VideoOceanengineDiagnosis.OE_MATERIAL_ID, data.getMaterialId())
                            .set(VideoOceanengineDiagnosis.OE_TASK_ID, data.getTaskId())
            );
            //提交预审
            commitAudio(task, data.getVideoId());
        } else {
            //失败
            videoOceanengineDiagnosisManager.update(
                    Wrappers.<VideoOceanengineDiagnosis>update()
                            .eq(VideoOceanengineDiagnosis.ID, task.getId())
                            .set(VideoOceanengineDiagnosis.STATUS, -1)
                            .set(VideoOceanengineDiagnosis.COMMIT_TIME, now)
            );
        }
    }

    private void commitAudio(VideoMaterialTask<Long> task, String oeVideoId) {
        Long videoId = task.getParams();
        //检查是否是 organize_id=35 的，如果不是，则不提交
        AiGenerateVideoOutput videoOutput = aiGenerateVideoOutputManager.getOne(
                Wrappers.<AiGenerateVideoOutput>query()
                        .eq(AiGenerateVideoOutput.TASK_ID, videoId)
                        .select(AiGenerateVideoOutput.ORGANIZE_ID)
                        .last(InngkeAppConst.STR_LIMIT_1)
        );
        if (videoOutput == null || !videoOutput.getOrganizeId().equals(35L)) {
            return;
        }

        String accessToken = oceanEngineAccessTokenService.getAccessToken();
        if (accessToken == null) {
            logger.error("获取OceanEngine AccessToken失败！");
            audioCommitError(task.getId());
            return;
        }

        OpenMaterialAuditRequest request = new OpenMaterialAuditRequest()
                .setAccountId(ADVERTISER_ID)
                .setBusinessType("AD")
                .setType("VIDEO")
                .setData(oeVideoId)
                .setMsgType("SEND");
        BaseVolcResponse<OpenMaterialAuditResponse> resp = oceanEngineVideoApi.openMaterialAudit(accessToken, request);
        if (resp == null || resp.getData() == null) {
            logger.error("提交预审任务失败: {}", JsonUtil.toJsonString(resp));
            audioCommitError(task.getId());
            return;
        }
        OpenMaterialAuditResponse result = resp.getData();
        if (result == null || !result.getResult()) {
            logger.error("提交预审任务失败: {}", JsonUtil.toJsonString(resp));
            audioCommitError(task.getId());
            return;
        }
        logger.info("提交预审任务成功：{}", JsonUtil.toJsonString(resp));
        videoOceanengineDiagnosisManager.update(
                Wrappers.<VideoOceanengineDiagnosis>update()
                        .eq(VideoOceanengineDiagnosis.ID, task.getId())
                        .set(VideoOceanengineDiagnosis.OE_OBJECT_ID, result.getObjectId())
                        .set(VideoOceanengineDiagnosis.AUDIT_STATUS, 1)
                        .set(VideoOceanengineDiagnosis.COMMIT_AUDIO_TIME, LocalDateTime.now())
        );
    }

    private void audioCommitError(long taskId) {
        videoOceanengineDiagnosisManager.update(
                Wrappers.<VideoOceanengineDiagnosis>update()
                        .eq(VideoOceanengineDiagnosis.ID, taskId)
                        .set(VideoOceanengineDiagnosis.AUDIT_STATUS, -1)
        );
    }

    @Scheduled(fixedRate = 30000, initialDelay = 6000)
    public void checkVideoOceanEngineDiagnosisResult() {
        String lockKey = CrmServiceConsts.CACHE_LOCK_KEY_PRE + "checkVideoOceanEngineDiagnosisResult";
        Lock lock = lockService.getLock(lockKey, 30, false);
        if (lock == null) {
            return;
        }

        //将超过一天的记录标识为失败
        videoOceanengineDiagnosisManager.update(
                Wrappers.<VideoOceanengineDiagnosis>update()
                        .eq(VideoOceanengineDiagnosis.STATUS, 1)
                        .lt(VideoOceanengineDiagnosis.COMMIT_TIME, LocalDateTime.now().minusDays(1)) //一天前的
                        .set(VideoOceanengineDiagnosis.STATUS, -2)
        );

        // 将超过一天的 预审任务标识为失败
        videoOceanengineDiagnosisManager.update(
                Wrappers.<VideoOceanengineDiagnosis>update()
                        .eq(VideoOceanengineDiagnosis.AUDIT_STATUS, 1)
                        .lt(VideoOceanengineDiagnosis.COMMIT_AUDIO_TIME, LocalDateTime.now().minusDays(1)) //一天前的
                        .set(VideoOceanengineDiagnosis.AUDIT_STATUS, -3)
        );

        //将超过15分钟的预审任务的记录，重新提交
        videoOceanengineDiagnosisManager.list(
                Wrappers.<VideoOceanengineDiagnosis>query()
                        .isNotNull(VideoOceanengineDiagnosis.OE_VIDEO_ID)
                        .eq(VideoOceanengineDiagnosis.AUDIT_STATUS, -1)
                        .select(VideoOceanengineDiagnosis.ID, VideoOceanengineDiagnosis.OE_VIDEO_ID, VideoOceanengineDiagnosis.VIDEO_ID)
        ).forEach(item -> {
            logger.info("重新提交预审任务：{}", item.getVideoId());
            commitAudio(new VideoMaterialTask<Long>().setId(item.getId()).setParams(item.getVideoId()), item.getOeVideoId());
        });

        Map<Long, VideoOceanengineDiagnosis> taskMap = videoOceanengineDiagnosisManager.list(
                Wrappers.<VideoOceanengineDiagnosis>query()
                        .eq(VideoOceanengineDiagnosis.STATUS, 1)
                        .gt(VideoOceanengineDiagnosis.OE_TASK_ID, 0)
                        .lt(VideoOceanengineDiagnosis.COMMIT_TIME, LocalDateTime.now().minusMinutes(1)) //前一分钟以前提交的任务
                        .orderByAsc(VideoOceanengineDiagnosis.LAST_CHECK_TIME)
                        .last(InngkeAppConst.STR_LIMIT + 20)
                        .select(VideoOceanengineDiagnosis.ID, VideoOceanengineDiagnosis.OE_TASK_ID)
        ).stream().collect(Collectors.toMap(VideoOceanengineDiagnosis::getOeTaskId, Function.identity()));

        if (CollectionUtils.isEmpty(taskMap)) {
            return;
        }

        String accessToken = oceanEngineAccessTokenService.getAccessToken();
        if (accessToken == null) {
            logger.error("获取OceanEngine AccessToken失败！");
            return;
        }

        String taskIds = JsonUtil.toJsonString(taskMap.keySet());
        logger.info("首发检测：{}", taskIds);
        BaseVolcResponse<VideoOceanEngineDiagnosisResultResponse> resp = oceanEngineVideoApi.getVideoOceanEngineDiagnosisResults(accessToken, ADVERTISER_ID, taskIds);
        if (resp == null || resp.getData() == null || CollectionUtils.isEmpty(resp.getData().getTaskList())) {
            logger.warn("查询首发检测结果为空");
            return;
        }
        resp.getData().getTaskList().forEach(taskItem -> {
            VideoOceanengineDiagnosis videoOceanengineDiagnosis = taskMap.get(taskItem.getTaskId());
            if (videoOceanengineDiagnosis == null) {
                return;
            }
            switch (taskItem.getStatus()) {
                case VideoOceanEngineDiagnosisTask.STATUS_SUCCESS:
                    videoOceanEngineDiagnosisTaskSuccess(videoOceanengineDiagnosis, taskItem);
                    break;
                case VideoOceanEngineDiagnosisTask.STATUS_FAILED:
                    videoOceanEngineDiagnosisTaskFail(videoOceanengineDiagnosis, taskItem);
                    break;
                default:
                    videoOceanEngineDiagnosisTaskNoResult(videoOceanengineDiagnosis, taskItem);
                    break;
            }
        });
    }

    private void videoOceanEngineDiagnosisTaskFail(VideoOceanengineDiagnosis videoOceanengineDiagnosis, VideoOceanEngineDiagnosisTask taskItem) {
        videoOceanengineDiagnosisManager.update(
                Wrappers.<VideoOceanengineDiagnosis>update()
                        .eq(VideoOceanengineDiagnosis.ID, videoOceanengineDiagnosis.getId())
                        .set(VideoOceanengineDiagnosis.STATUS, -2)
                        .set(VideoOceanengineDiagnosis.FINISH_TIME, LocalDateTime.now())
                        .set(VideoOceanengineDiagnosis.LAST_CHECK_TIME, (int) System.currentTimeMillis() / 1000)
        );
    }

    private void videoOceanEngineDiagnosisTaskSuccess(VideoOceanengineDiagnosis videoOceanengineDiagnosis, VideoOceanEngineDiagnosisTask taskItem) {
        videoOceanengineDiagnosisManager.update(
                Wrappers.<VideoOceanengineDiagnosis>update()
                        .eq(VideoOceanengineDiagnosis.ID, videoOceanengineDiagnosis.getId())
                        .set(VideoOceanengineDiagnosis.STATUS, 2)
                        .set(VideoOceanengineDiagnosis.FINISH_TIME, LocalDateTime.now())
                        .set(VideoOceanengineDiagnosis.IS_AD_HIGH_QUALITY_MATERIAL, taskItem.getIsAdHighQualityMaterial())
                        .set(VideoOceanengineDiagnosis.IS_FIRST_PUBLISH_MATERIAL, taskItem.getIsFirstPublishMaterial())
                        .set(VideoOceanengineDiagnosis.IS_ECP_HIGH_QUALITY_MATERIAL, taskItem.getIsEcpHighQualityMaterial())
                        .set(VideoOceanengineDiagnosis.IS_INEFFICIENT_MATERIAL, taskItem.getIsInefficientMaterial())
                        .set(!CollectionUtils.isEmpty(taskItem.getNotAdHighQualityReason()), VideoOceanengineDiagnosis.NOT_AD_HIGH_QUALITY_REASON, JsonUtil.toJsonString(taskItem.getNotAdHighQualityReason()))
                        .set(!CollectionUtils.isEmpty(taskItem.getNotEcpHighQualityReason()), VideoOceanengineDiagnosis.NOT_ECP_HIGH_QUALITY_REASON, JsonUtil.toJsonString(taskItem.getNotEcpHighQualityReason()))
                        .set(VideoOceanengineDiagnosis.LAST_CHECK_TIME, (int) System.currentTimeMillis() / 1000)
        );
    }

    public void retryDiagnosis(List<Long> videoIds) {
        if (CollectionUtils.isEmpty(videoIds)) {
            return;
        }
        aiGenerateVideoOutputManager.list(
                Wrappers.<AiGenerateVideoOutput>query()
                        .in(AiGenerateVideoOutput.TASK_ID, videoIds)
                        .select(AiGenerateVideoOutput.TASK_ID, AiGenerateVideoOutput.VIDEO_URL)
        ).forEach(video -> {
            String videoUrl = video.getVideoUrl();
            if (!StringUtils.hasLength(videoUrl)) {
                return;
            }
            createOceanEngineDiagnosis(video.getTaskId(), videoUrl);
        });
    }

    public void createOceanEngineDiagnosis(long taskId, String videoUrl) {
        //写数据库
        VideoOceanengineDiagnosis videoOceanengineDiagnosis = videoOceanengineDiagnosisManager.getOne(
                Wrappers.<VideoOceanengineDiagnosis>query()
                        .eq(VideoOceanengineDiagnosis.VIDEO_ID, taskId)
                        .select(VideoOceanengineDiagnosis.ID)
        );
        if (videoOceanengineDiagnosis != null) {
            //更新状态
            videoOceanengineDiagnosisManager.update(
                    Wrappers.<VideoOceanengineDiagnosis>update()
                            .eq(VideoOceanengineDiagnosis.ID, videoOceanengineDiagnosis.getId())
                            .set(VideoOceanengineDiagnosis.STATUS, 0)
            );
        } else {
            //新增
            videoOceanengineDiagnosis = new VideoOceanengineDiagnosis();
            videoOceanengineDiagnosis.setId(snowflakeIdService.getId());
            videoOceanengineDiagnosis.setVideoId(taskId);
            videoOceanengineDiagnosis.setStatus(0);
            videoOceanengineDiagnosis.setCreateTime(LocalDateTime.now());
            videoOceanengineDiagnosisManager.save(videoOceanengineDiagnosis);
        }

        VideoMaterialTask<Long> task = new VideoMaterialTask<Long>()
                .setId(videoOceanengineDiagnosis.getId())
                .setUrl(videoUrl)
                .setType(VideoOceanEngineDiagnosisTaskHandler.TASK_TYPE)
                .setParams(taskId);
        videoMaterialTaskService.submitRotateTask(task);
    }

    private void videoOceanEngineDiagnosisTaskNoResult(VideoOceanengineDiagnosis videoOceanengineDiagnosis, VideoOceanEngineDiagnosisTask taskItem) {
        videoOceanengineDiagnosisManager.update(
                Wrappers.<VideoOceanengineDiagnosis>update()
                        .eq(VideoOceanengineDiagnosis.ID, videoOceanengineDiagnosis.getId())
                        .set(VideoOceanengineDiagnosis.LAST_CHECK_TIME, (int) System.currentTimeMillis() / 1000)
        );
    }
}
