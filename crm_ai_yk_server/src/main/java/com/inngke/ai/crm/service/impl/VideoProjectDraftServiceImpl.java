package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.collect.Queues;
import com.inngke.ai.crm.api.browser.JiChuangBrowserApi;
import com.inngke.ai.crm.api.browser.dto.jichaung.SensitiveDto;
import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.crm.client.video.VideoCreateServiceForCrm;
import com.inngke.ai.crm.converter.VideoBgmMaterialConverter;
import com.inngke.ai.crm.converter.VideoProjectDraftConverter;
import com.inngke.ai.crm.core.CrmServiceConsts;
import com.inngke.ai.crm.core.util.RetryTaskUtils;
import com.inngke.ai.crm.core.util.VideoScriptUtils;
import com.inngke.ai.crm.db.crm.entity.*;
import com.inngke.ai.crm.db.crm.manager.*;
import com.inngke.ai.crm.dto.enums.VideoDraftTypeEnum;
import com.inngke.ai.crm.dto.exceptions.NoRetryException;
import com.inngke.ai.crm.dto.request.devops.VideoBgmListRequest;
import com.inngke.ai.crm.dto.request.video.*;
import com.inngke.ai.crm.dto.response.video.*;
import com.inngke.ai.crm.service.*;
import com.inngke.ai.dto.*;
import com.inngke.ai.dto.enums.DigitalPersonDisplayEnum;
import com.inngke.ai.dto.enums.VideoTypeEnum;
import com.inngke.ai.dto.request.*;
import com.inngke.ai.dto.response.MaterialSubAtaDto;
import com.inngke.ai.dto.response.PreviewMashupVideoDto;
import com.inngke.ai.dto.utils.FormDataUtils;
import com.inngke.ai.dto.widget.WidgetGroup;
import com.inngke.common.core.InngkeAppConst;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.core.utils.SnowflakeHelper;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.Lock;
import com.inngke.common.dto.response.BasePaginationResponse;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.LockService;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.DateTimeUtils;
import com.inngke.common.utils.JsonUtil;
import com.inngke.common.utils.Md5Utils;
import com.inngke.ip.ai.dify.app.VideoDigitalHumanConfigApp;
import com.inngke.ip.ai.dify.app.VideoMashupApp;
import com.inngke.ip.ai.dify.app.VideoStoreScriptFixerApp;
import com.inngke.ip.ai.dify.app.VideoSubtitleTypesetApp;
import com.inngke.ip.ai.dify.app.dto.*;
import com.inngke.ip.ai.dify.utils.DifyRequestInputsBuilder;
import com.inngke.ip.ai.volc.api.VolcTtsApi;
import com.inngke.ip.ai.volc.config.VolcProperties;
import com.inngke.ip.ai.volc.dto.response.VolcAtaQueryResponse;
import com.inngke.ip.ai.volc.dto.response.VolcAtaUtterance;
import com.inngke.ip.ai.volc.dto.response.BeatTrackingResult;
import com.inngke.ip.ai.dify.app.VideoScriptSceneApp;
import com.inngke.ip.ai.vector.dto.MaterialInfoDto;
import com.inngke.ip.ai.volc.service.VolcSamiService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import retrofit2.Response;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.inngke.ip.ai.dify.app.VideoScriptSceneApp.SUBTITLE_SPLITTER_VERSION_BACKSLASH;


@Service
public class VideoProjectDraftServiceImpl implements VideoProjectDraftService {
    private static final Logger logger = LoggerFactory.getLogger(VideoProjectDraftServiceImpl.class);

    public static final String DIGITAL_PERSON_CONFIG_CACHE_KEY = CrmServiceConsts.CACHE_KEY_PRE + "dp:config";
    public static final String STR_USER_VIDEO_CONFIG = "userVideoConfig:";
    public static final int DEFAULT_SUBTITLE_POSITION_Y = -200;
    public static final String STR_SUBTITLE_CONFIG = "subtitleConfig";
    public static final List<String> CONVENTION_CACHE_FORM_KEY = Lists.newArrayList(
            FormDataUtils.FORM_KEY_VIDEO_LUT,
            FormDataUtils.FORM_KEY_SUBTITLE_TAG_STYLE,
            FormDataUtils.FORM_KEY_VERTICAL,
            FormDataUtils.FORM_KEY_DIGITAL_PERSON_SWITCH,
            FormDataUtils.FORM_KEY_SCENE_VIDEO_DISPLAY,
            FormDataUtils.FORM_KEY_TRANSITION_TYPE,
            FormDataUtils.FORM_KEY_AUDIO_DENOISE,
            FormDataUtils.FORM_KEY_BGM_OPEN,
            FormDataUtils.FORM_KEY_BGM_SHOCK_END,
            FormDataUtils.FORM_KEY_BEAT
    );

    @Autowired
    private VideoSubtitleTypesetApp videoSubtitleTypesetApp;

    @Autowired
    private VideoDigitalHumanConfigApp videoDigitalHumanConfigApp;

    @Autowired
    private DifyAppConfService difyAppConfService;

    @Autowired
    private VideoProjectDraftManager videoProjectDraftManager;

    @Autowired
    private LockService lockService;

    @Autowired
    private StaffService staffService;

    @Autowired
    private VideoBgmMaterialManager videoBgmMaterialManager;

    @Autowired
    private DigitalPersonTemplateManager digitalPersonTemplateManager;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private TtsConfigManager ttsConfigManager;

    @Autowired
    private DifyService difyService;

    @Autowired
    private VideoApi videoApi;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private TextFontManager textFontManager;

    @Autowired
    private JianyingResourceManager jianyingResourceManager;

    @Autowired
    private DownloadService downloadService;

    @Autowired
    private UploadService uploadService;

    @Autowired
    private VolcProperties volcProperties;

    @Autowired
    private AiGenerateTaskIoManager aiGenerateTaskIoManager;

    @Autowired
    private AiGenerateVideoOutputManager aiGenerateVideoOutputManager;

    @Autowired
    private VolcTtsApi volcTtsApi;

    @Autowired
    private JiChuangBrowserApi jiChuangBrowserApi;

    @Autowired
    private VideoStoreScriptFixerApp videoStoreScriptFixerApp;

    @Autowired
    private VideoMaterialService videoMaterialService;

    @Autowired
    private VideoMashupApp videoMashupApp;

    @Autowired
    private VideoMashupScriptManager videoMashupScriptManager;

    @Autowired
    private VideoScriptSceneApp videoScriptSceneApp;

    @Autowired
    private VolcSamiService volcSamiService;

    @Autowired
    private VideoCreateServiceForCrm videoCreateService;
    @Autowired
    private AiGenerateTaskManager aiGenerateTaskManager;

    @Override
    public BasePaginationResponse<VideoProjectDraftDto> getDraftList(JwtPayload jwtPayload, VideoProjectDraftRequest request) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        QueryWrapper<VideoProjectDraft> queryWrapper = Wrappers.<VideoProjectDraft>query()
                .eq(VideoProjectDraft.STAFF_ID, staff.getId())
                .eq(VideoProjectDraft.DELETED, false)
                .orderByDesc(VideoProjectDraft.UPDATE_TIME);

        if (!StringUtils.isEmpty(request.getKeyword())) {
            queryWrapper.like(VideoProjectDraft.TITLE, request.getKeyword());
        }

        // 手动实现分页
        int pageNo = request.getPageNo();
        int pageSize = request.getPageSize();
        int offset = (pageNo - 1) * pageSize;

        // 获取总记录数
        int total = videoProjectDraftManager.count(queryWrapper);

        // 获取分页数据
        queryWrapper.last("LIMIT " + offset + "," + pageSize);
        List<VideoProjectDraft> records = videoProjectDraftManager.list(queryWrapper);

        List<VideoProjectDraftDto> draftDtos = records.stream()
                .map(VideoProjectDraftConverter::toVideoProjectDraftDto)
                .collect(Collectors.toList());

        BasePaginationResponse<VideoProjectDraftDto> response = new BasePaginationResponse<>();
        response.setList(draftDtos);
        response.setTotal(total);

        return response;
    }

    @Override
    public List<VideoProjectDraftDto> getDraftDemos(JwtPayload jwtPayload) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        QueryWrapper<VideoProjectDraft> queryWrapper = Wrappers.<VideoProjectDraft>query()
                .eq(VideoProjectDraft.ORGANIZE_ID, Lists.newArrayList(staff.getOrganizeId(), 0L))
                .eq(VideoProjectDraft.DELETED, false)
                .gt(VideoProjectDraft.RECOMMEND, 0)
                .orderByDesc(VideoProjectDraft.RECOMMEND, VideoProjectDraft.CREATE_TIME)
                .last("LIMIT 10");

        List<VideoProjectDraft> draftDemos = videoProjectDraftManager.list(queryWrapper);
        Map<Long, List<VideoProjectDraft>> draftDemoGroup = draftDemos.stream().collect(Collectors.groupingBy(VideoProjectDraft::getOrganizeId));
        List<VideoProjectDraft> videoProjectDrafts = Optional.ofNullable(draftDemoGroup.get(staff.getOrganizeId())).orElse(draftDemoGroup.get(0L));

        if (CollectionUtils.isEmpty(videoProjectDrafts)) {
            return Lists.newArrayList();
        }

        return videoProjectDrafts.stream().map(VideoProjectDraftConverter::toVideoProjectDraftDto).collect(Collectors.toList());
    }

    @Override
    public VideoProjectDraftDetail cloneFromDraft(JwtPayload jwtPayload, VideoProjectDraftCreateRequest request) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        if (Objects.isNull(request.getDraftId())) {
            return cloneFromTask(staff, request.getTaskId());
        }

        VideoProjectDraft originalDraft = videoProjectDraftManager.getById(request.getDraftId());
        if (originalDraft == null) {
            throw new InngkeServiceException("原创作不存在");
        }

        if (!originalDraft.getOrganizeId().equals(staff.getOrganizeId()) && originalDraft.getRecommend() <= 0) {
            throw new InngkeServiceException("草稿不存在或无权限访问");
        }

        VideoProjectDraft newDraft = new VideoProjectDraft();

        Long newDraftId = SnowflakeHelper.getId();
        newDraft.setId(newDraftId);
        newDraft.setStaffId(staff.getId());
        newDraft.setOrganizeId(staff.getOrganizeId());
        newDraft.setType(request.getType());
        newDraft.setTitle("副本_" + Optional.ofNullable(originalDraft.getTitle()).orElse(originalDraft.getId().toString()));
        newDraft.setCoverImage(originalDraft.getCoverImage());
        VideoCreateWithMaterialRequest projectContext = JsonUtil.jsonToObject(originalDraft.getProjectContext(), VideoCreateWithMaterialRequest.class);
        projectContext.setTaskId(newDraftId);
        projectContext.setDraftId(newDraftId);

        //裂变类型，初始化备选素材为选中的素材
        if (request.getType().equals(1)) {
            mashupCloneSettingProjectContext(projectContext);
        }

        setProjectMaterialRotate(projectContext);
        newDraft.setProjectContext(JsonUtil.toJsonString(projectContext));
        newDraft.setCreateType(2);
        newDraft.setCreateFromId(originalDraft.getId());
        newDraft.setCreateTime(LocalDateTime.now());
        newDraft.setUpdateTime(LocalDateTime.now());

        videoProjectDraftManager.save(newDraft);

        //调用video_ai_yk创建目录，复制一些文件，比如：keywords.txt
        AsyncUtils.runAsync(() -> {
            VideoTaskCloneRequest cloneRequest = new VideoTaskCloneRequest()
                    .setNewTaskId(newDraftId)
                    .setFromTaskId(request.getDraftId());
            videoApi.cloneVideoTask(cloneRequest);
        });

        return VideoProjectDraftConverter.toVideoProjectDraftDetail(newDraft);
    }

    private void mashupCloneSettingProjectContext(VideoCreateWithMaterialRequest projectContext) {
        projectContext.getScripts().forEach(script -> {
            List<Long> materialIds = script.getMaterialList().stream().map(VideoMaterialItem::getMaterialId).collect(Collectors.toList());
            List<VideoMaterialItem> materiaList = Lists.newArrayList();
            materiaList.addAll(0, script.getMaterialList());
            script.setMaterialOriginList(materiaList);
        });
        projectContext.setChooseDigitalHumanConfigs(projectContext.getDigitalHumanConfigs());
        Optional.ofNullable(projectContext.getBeforeVideo())
                .ifPresentOrElse(
                        beforeVideo -> projectContext.setBeforeScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList(beforeVideo))),
                        () -> projectContext.setBeforeScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList()))
                );
        Optional.ofNullable(projectContext.getBeforeScript())
                .ifPresentOrElse(
                        projectContext::setBeforeScript,
                        () -> projectContext.setBeforeScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList()))
                );

        Optional.ofNullable(projectContext.getAfterVideo())
                .ifPresentOrElse(
                        afterVideo -> projectContext.setAfterScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList(afterVideo))),
                        () -> projectContext.setAfterScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList()))
                );
        Optional.ofNullable(projectContext.getAfterScript())
                .ifPresentOrElse(
                        projectContext::setAfterScript,
                        () -> projectContext.setAfterScript(new VideoUserScriptDto().setMaterialList(Lists.newArrayList()))
                );

        Map<String, Object> promptMap = projectContext.getPromptMap();
        Optional.ofNullable(promptMap.get(FormDataUtils.FORM_KEY_CHOOSE_MUSIC)).ifPresentOrElse(mid -> {
            if (StringUtils.isNotBlank(mid.toString())) {
                promptMap.put(FormDataUtils.FORM_KEY_CHOOSE_MUSIC_IDS, Lists.newArrayList(mid));
            } else {
                promptMap.put(FormDataUtils.FORM_KEY_CHOOSE_MUSIC_IDS, Lists.newArrayList());
            }
        }, () -> promptMap.put(FormDataUtils.FORM_KEY_CHOOSE_MUSIC_IDS, Lists.newArrayList()));
        //fixme 删除
        promptMap.put("videoCategoryId", InngkeAppConst.EMPTY_STR);
        projectContext.setVideoCategoryId(null);
    }

    private VideoProjectDraftDetail cloneFromTask(Staff staff, Long taskId) {
        if (Objects.isNull(taskId)) {
            throw new InngkeServiceException("任务ID与创作ID不能同时为空");
        }

        AiGenerateTaskIo aiGenerateTaskIo = aiGenerateTaskIoManager.getById(taskId);
        AiGenerateTask aiGenerateTask = aiGenerateTaskManager.getById(taskId);
        AiGenerateVideoOutput aiGenerateVideoOutput = aiGenerateVideoOutputManager.getByTaskIds(Lists.newArrayList(taskId))
                .stream().collect(Collectors.toMap(AiGenerateVideoOutput::getTaskId, Function.identity())).get(taskId);

        if (Objects.isNull(aiGenerateTaskIo)) {
            throw new InngkeServiceException("获取任务信息失败");
        }
        Long newDraftId = SnowflakeHelper.getId();
        VideoCreateWithMaterialRequest projectContext = JsonUtil.jsonToObject(aiGenerateTaskIo.getInputs(), VideoCreateWithMaterialRequest.class);
        projectContext.setTaskId(newDraftId);
        projectContext.setDraftId(newDraftId);

        setProjectMaterialRotate(projectContext);

        mashupCloneSettingProjectContext(projectContext);
        Map<String, Object> promptMap = projectContext.getPromptMap();
        int videoType = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VIDEO_TYPE, 0);

        VideoProjectDraft videoProjectDraft = new VideoProjectDraft();
        videoProjectDraft.setId(newDraftId);
        videoProjectDraft.setStaffId(staff.getId());
        videoProjectDraft.setOrganizeId(staff.getOrganizeId());
        videoProjectDraft.setType(2);
        videoProjectDraft.setTitle("副本_" + Optional.ofNullable(aiGenerateVideoOutput).map(AiGenerateVideoOutput::getVideoTitle).orElse(taskId.toString()));
        videoProjectDraft.setProjectContext(JsonUtil.toJsonString(projectContext));
        videoProjectDraft.setCreateType(10);
        videoProjectDraft.setCoverImage(
                Optional.ofNullable(aiGenerateVideoOutput).map(AiGenerateVideoOutput::getVideoUrl)
                        .map(url -> url + "?x-oss-process=video/snapshot,t_0,f_jpg").orElse(null)
        );
        videoProjectDraft.setCreateFromId(taskId);
        videoProjectDraft.setCreateTime(LocalDateTime.now());
        videoProjectDraft.setMatchedMaterial(true);

        // 踩点音乐
        if (VideoTypeEnum.MUSIC_BEAT_VIDEO.getVideoType() == videoType) {
            videoProjectDraft.setType(4);
            videoProjectDraft.setTitle("副本_" + Optional.ofNullable(aiGenerateTask).map(AiGenerateTask::getTitle).orElse(taskId.toString()));
        }

        videoProjectDraftManager.save(videoProjectDraft);

        //调用video_ai_yk创建目录，复制一些文件，比如：keywords.txt
        AsyncUtils.runAsync(() -> {
            VideoTaskCloneRequest cloneRequest = new VideoTaskCloneRequest()
                    .setNewTaskId(newDraftId)
                    .setFromTaskId(taskId);
            videoApi.cloneVideoTask(cloneRequest);
        });

        return VideoProjectDraftConverter.toVideoProjectDraftDetail(videoProjectDraft);
    }

    @Override
    public boolean setTitle(JwtPayload jwtPayload, VideoProjectDraftTitleSetRequest request) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        VideoProjectDraft draft = videoProjectDraftManager.getById(request.getDraftId());
        if (draft == null || !draft.getStaffId().equals(staff.getId())) {
            throw new InngkeServiceException("草稿不存在或无权限修改");
        }

        return videoProjectDraftManager.update(Wrappers.<VideoProjectDraft>update()
                .eq(VideoProjectDraft.ID, request.getDraftId())
                .set(VideoProjectDraft.TITLE, request.getTitle())
        );
    }

    @Override
    public VideoProjectDraftDetail saveDraft(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request, String draftTitle) {
        Long userId = jwtPayload.getCid();
        Staff staff = staffService.getStaffByUserId(userId);
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        Long draftId = Optional.ofNullable(request.getDraftId()).orElse(SnowflakeHelper.getId());

        VideoProjectDraft draft = this.initProjectDraft(staff, draftId, draftTitle, request);

        //兼容script中存在null的脚本
        request.setScripts(request.getScripts().stream().filter(Objects::nonNull).collect(Collectors.toList()));

        //是否已经匹配素材
        VideoMaterialItem anyMaterial = request.getScripts().stream().filter(Objects::nonNull).findFirst().map(
                VideoUserScriptDto::getMaterialOriginList
        ).map(Collection::stream).map(Stream::findFirst).flatMap(Function.identity()).orElse(null);
        draft.setMatchedMaterial(Objects.nonNull(anyMaterial));

        VideoProjectDraft exist = videoProjectDraftManager.getById(draftId);
        Integer recommend = Optional.ofNullable(exist).map(VideoProjectDraft::getRecommend).orElse(0);
        //案例不修改
        if (recommend > 0) {
            return VideoProjectDraftConverter.toVideoProjectDraftDetail(exist);
        }

        if (Objects.nonNull(exist)) {
            //修改
            updateCoverImage(request, exist);
            draft.setStaffId(null);
            videoProjectDraftManager.updateById(draft);
        } else {
            //新增
            draft.setCreateTime(LocalDateTime.now());
            videoProjectDraftManager.save(draft);
        }
        List<WidgetGroup> widgets = request.getWidgets();

        Long widgetId = null;
        if (!CollectionUtils.isEmpty(widgets)) {
            widgetId = widgets.get(0).getId();
        }
        saveUserVideoConfig(request.getPromptMap(), userId, widgetId);

        return VideoProjectDraftConverter.toVideoProjectDraftDetail(draft);
    }

    @Override
    public void saveUserVideoConfig(Map<String, Object> promptMap, Long userId, Long widgetId) {
        String key = CrmServiceConsts.CACHE_KEY_PRE + STR_USER_VIDEO_CONFIG + userId;
        HashOperations hashOps = redisTemplate.opsForHash();
        Map<String, Object> cacheData = hashOps.entries(key);
        if (cacheData == null) {
            cacheData = Maps.newHashMap();
        }

        int videoType = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VIDEO_TYPE, 1);

        // 缓存常规的表单值
        for (String formKey : CONVENTION_CACHE_FORM_KEY) {
            Object value = promptMap.get(formKey);

            if (value != null) {
                cacheData.put(formKey, value);
            }
        }

        // 不是音乐踩点
        if (VideoTypeEnum.MUSIC_BEAT_VIDEO.getVideoType() != videoType) {
            // subtitleConfig
            cacheData.put(STR_SUBTITLE_CONFIG, getSubtitleConfig(promptMap));
        }

        //贴片
        if (widgetId != null) {
            cacheData.put(VideoWidgetServiceImpl.LAST_USE_VIDEO_WIDGET_ID, widgetId);
        }

        hashOps.putAll(key, cacheData);
    }

    private void cacheIfNotNull(Map<String, Object> promptMap, String key, Map<String, Object> cacheData) {
        Object value = promptMap.get(key);
        if (value != null) {
            cacheData.put(key, value);
        }
    }

    @Override
    public VideoProjectVideoMashupDetail saveVideoMashupDraft(JwtPayload jwtPayload, VideoCreateWithVideoMashupRequest request) {
        Long userId = jwtPayload.getCid();
        Staff staff = staffService.getStaffByUserId(userId);
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        Long draftId = Optional.ofNullable(request.getDraftId()).orElse(SnowflakeHelper.getId());

        VideoProjectDraft draft = this.initProjectDraft(staff, draftId, null, request);
        draft.setType(3);
        if (StringUtils.isNotBlank(request.getMashupTitle())) {
            draft.setTitle(request.getMashupTitle());
        }

        VideoProjectDraft exist = videoProjectDraftManager.getById(draftId);

        List<OralVideoMaterial> mashupVideoList = FormDataUtils.getOralVideoMaterialList(
                request.getPromptMap(), FormDataUtils.FORM_KEY_ORAL_VIDEO_MATERIAL
        );

        //封面图片
        if (!CollectionUtils.isEmpty(mashupVideoList)) {
            mashupVideoList.stream().findFirst().map(OralVideoMaterial::getUrl)
                    .ifPresent(url -> draft.setCoverImage(url + "?x-oss-process=video/snapshot,t_0,f_jpg,m_fast"));
        }

        if (exist != null) {
            videoProjectDraftManager.updateById(draft);
        } else {
            draft.setCreateTime(LocalDateTime.now());
            videoProjectDraftManager.save(draft);
        }

        return this.getVideoMashupDraftDetail(jwtPayload, draftId);
    }

    private VideoProjectDraft initProjectDraft(Staff staff, Long draftId, String draftTitle, Serializable request) {
        VideoProjectDraft draft = new VideoProjectDraft();
        draft.setId(draftId);
        draft.setStaffId(staff.getId());
        draft.setOrganizeId(staff.getOrganizeId());
        draft.setProjectContext(JsonUtil.toJsonString(request));
        if (StringUtils.isNotBlank(draftTitle)) {
            draft.setTitle(draftTitle);
        }

        return draft;
    }

    @Override
    public VideoProjectVideoMashupDetail draftVideoMashupScript(JwtPayload jwtPayload, VideoCreateWithVideoMashupRequest request) {
        List<OralVideoMaterial> mashupVideoList = FormDataUtils.getOralVideoMaterialList(
                request.getPromptMap(), FormDataUtils.FORM_KEY_ORAL_VIDEO_MATERIAL
        );
        List<Long> mashupMaterialIds = mashupVideoList.stream().map(OralVideoMaterial::getMaterialId).collect(Collectors.toList());

        Map<Long, List<VideoUtteranceDto>> mashupVideoUtteranceMap = Maps.newHashMap();

        List<VideoUtteranceInfoDto> videoUtteranceInfoList = mashupVideoList.stream().map(video -> {
            VideoUtteranceInfoDto videoUtteranceInfoDto = new VideoUtteranceInfoDto();
            videoUtteranceInfoDto.setId(video.getMaterialId());
            List<VideoUtteranceDto> videoUtterance = getVideoUtterance(video.getUrl());
            if (CollectionUtils.isEmpty(videoUtterance)) {
                return null;
            }
            videoUtteranceInfoDto.setUtteranceList(videoUtterance);
            return videoUtteranceInfoDto;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        // 初始化视频素材的utteranceList
        AsyncUtils.runAsync(() -> {
            CheckSubtitleMaterialAtaRequest checkSubtitleAtaRequest = new CheckSubtitleMaterialAtaRequest();
            checkSubtitleAtaRequest.setMaterialList(mashupVideoList);
            videoMaterialService.checkSubtitleAta(checkSubtitleAtaRequest);
        });

        //调用dify工作流
        DifyRequestInputsBuilder inputsBuilder = DifyRequestInputsBuilder.newBuilder()
                .set("videoIdeas", FormDataUtils.getString(request.getPromptMap(), FormDataUtils.FORM_KEY_VIDEO_IDEAS))
                .set("videoNum", FormDataUtils.getInt(request.getPromptMap(), FormDataUtils.FORM_KEY_VIDEO_NUM, 1))
                .set("videoUtteranceList", JsonUtil.toJsonString(videoUtteranceInfoList));

        VideoMashupResponse videoMashupResponse = videoMashupApp.execute(
                difyService.getUser(jwtPayload.getCid()), inputsBuilder.getInputs()
        );

        List<VideoMashupDto> mashupList = videoMashupResponse.getMashupList();

        mashupList = mashupList.stream().filter(mashup -> {
            for (VideoMashupScriptDto script : mashup.getScripts()) {
                if (!mashupMaterialIds.contains(script.getId())) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(mashupList)) {
            throw new InngkeServiceException("本次创作繁忙，请重新点击生成分镜脚本！");
        }

        //删除旧数据
        if (Objects.nonNull(request.getDraftId())) {
            videoMashupScriptManager.removeByDraftId(request.getDraftId());
        }
        if (Objects.isNull(request.getDraftId())) {
            request.setDraftId(SnowflakeHelper.getId());
        }

        //保存新数据
        List<VideoMashupScript> scriptList = mashupList.stream().map(mashup -> {
            fixVideoMashupScript(mashup.getScripts(), mashupVideoList);
            VideoMashupScript script = new VideoMashupScript();
            script.setId(SnowflakeHelper.getId());
            script.setDraftId(request.getDraftId());
            script.setScripts(JsonUtil.toJsonString(mashup.getScripts()));
            script.setSelected(true);
            script.setSubtitle(Optional.ofNullable(mashup.getSubtitle())
                    .orElse(mashup.getScripts().stream().map(VideoMashupScriptDto::getSubtitle).collect(Collectors.joining()))
            );
            script.setTitle(mashup.getTitle());
            script.setCreateTime(LocalDateTime.now());
            return script;
        }).collect(Collectors.toList());
        videoMashupScriptManager.saveBatch(scriptList);

        videoMashupResponse.getMashupList().forEach(videoMashupDto -> videoMashupDto.setSubtitle(
                videoMashupDto.getScripts().stream().map(VideoMashupScriptDto::getSubtitle).collect(Collectors.joining())
        ));
        videoMashupResponse.getMashupList().forEach(videoMashupDto -> videoMashupDto.setScripts(null));

        request.setVideoMashupList(videoMashupResponse.getMashupList());
        request.setMashupTitle(mashupList.stream().findFirst().map(VideoMashupDto::getTitle).orElse(InngkeAppConst.EMPTY_STR));

        VideoProjectVideoMashupDetail result = this.saveVideoMashupDraft(jwtPayload, request);

        return result;
    }

    private void fixVideoMashupScript(List<VideoMashupScriptDto> mashupScriptList, List<OralVideoMaterial> mashupVideoList) {
        mashupScriptList.forEach(script -> StringUtils.strip(script.getSubtitle(), SUBTITLE_SPLITTER_VERSION_BACKSLASH));

        Map<String, List<VideoMashupScriptDto>> scriptMaterialMap = mashupScriptList.stream().collect(
                Collectors.groupingBy(script -> script.getId() + "_" + script.getClipStart())
        );

        SubtitleSearchRequest subtitleSearchRequest = new SubtitleSearchRequest();
        subtitleSearchRequest.setVideoMaterialList(mashupVideoList);

        scriptMaterialMap.forEach((idAndStartTime, scriptList) -> {
            if (scriptList.size() < 2) {
                return;
            }
            scriptList.forEach(script -> {
                subtitleSearchRequest.setKeyword(script.getSubtitle().replace(SUBTITLE_SPLITTER_VERSION_BACKSLASH, InngkeAppConst.EMPTY_STR));
                List<MaterialSubAtaDto> materialSubAtaList = videoMaterialService.subtitleSearch(subtitleSearchRequest);
                if (CollectionUtils.isEmpty(materialSubAtaList)) {
                    return;
                }

                MaterialSubAtaDto materialSubAtaDto = materialSubAtaList.get(0);
                if (CollectionUtils.isEmpty(materialSubAtaDto.getSubAta())) {
                    return;
                }

                script.setId(materialSubAtaDto.getId());
                script.setClipStart(
                        materialSubAtaDto.getSubAta().get(0).getStartTime()
                );
                script.setClipDuration(
                        materialSubAtaDto.getSubAta().get(materialSubAtaDto.getSubAta().size() - 1).getEndTime() - script.getClipStart()
                );
            });
        });
    }

    @Override
    public VideoProjectVideoMashupDetail getVideoMashupDraftDetail(JwtPayload jwtPayload, Long draftId) {
        Long userId = jwtPayload.getCid();
        Staff staff = staffService.getStaffByUserId(userId);
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        if (draftId == 0L) {
            String key = CrmServiceConsts.CACHE_KEY_PRE + STR_USER_VIDEO_CONFIG + userId;
            HashOperations hashOps = redisTemplate.opsForHash();
            Map userOpts = hashOps.entries(key);
            if (userOpts == null) {
                userOpts = Maps.newHashMap();
            }

            draftId = SnowflakeHelper.getId();
            VideoProjectVideoMashupDetail videoProjectVideoMashupDetail = new VideoProjectVideoMashupDetail();
            videoProjectVideoMashupDetail.setId(draftId);
            videoProjectVideoMashupDetail.setType(3);
            videoProjectVideoMashupDetail.setStaffId(staff.getId());
            videoProjectVideoMashupDetail.setOrganizeId(staff.getOrganizeId());
            VideoCreateWithVideoMashupRequest request = new VideoCreateWithVideoMashupRequest();
            request.setDraftId(draftId);
            request.setVideoMashupList(Lists.newArrayList());
            request.setBeforeVideoList(Lists.newArrayList());
            request.setAfterVideoList(Lists.newArrayList());
            HashMap<String, Object> promptMap = Maps.newHashMap();
            promptMap.put(FormDataUtils.FORM_KEY_ORAL_VIDEO_MATERIAL, Lists.newArrayList());
            promptMap.put(FormDataUtils.FORM_KEY_BGM_OPEN, userOpts.getOrDefault(FormDataUtils.FORM_KEY_BGM_OPEN, false));
            promptMap.put(FormDataUtils.FORM_KEY_AUDIO_DENOISE, userOpts.getOrDefault(FormDataUtils.FORM_KEY_AUDIO_DENOISE, 0));
            promptMap.put(FormDataUtils.FORM_KEY_TRANSITION_TYPE, userOpts.getOrDefault(FormDataUtils.FORM_KEY_TRANSITION_TYPE, 0));
            promptMap.put(FormDataUtils.FORM_KEY_CHOOSE_MUSIC_IDS, Lists.newArrayList());
            promptMap.put(FormDataUtils.FORM_KEY_SUBTITLE_OFF, true);
            promptMap.put(FormDataUtils.FORM_KEY_SCENE_VIDEO_DISPLAY, 4);
            promptMap.put(FormDataUtils.FORM_KEY_VIDEO_NUM, 1);

            request.setPromptMap(promptMap);

            videoProjectVideoMashupDetail.setProjectContent(request);
            return videoProjectVideoMashupDetail;
        }

        VideoProjectDraft draft = videoProjectDraftManager.getById(draftId);
        if (draft == null) {
            throw new InngkeServiceException("草稿不存在");
        }

        if (staff.getTester() == null || !staff.getTester()) {
            //不是测试账号时，不允许跨企业
            if (!draft.getOrganizeId().equals(staff.getOrganizeId()) && draft.getRecommend() <= 0) {
                throw new InngkeServiceException("草稿不存在或无权限访问");
            }
        }

        List<VideoMashupScript> videoMashupScripts = videoMashupScriptManager.getByDraftId(draftId);

        VideoProjectVideoMashupDetail result = VideoProjectDraftConverter.videoProjectVideoMashupDetail(draft, videoMashupScripts);

        String previewVideoUrl = result.getProjectContent().getVideoMashupList().stream().findFirst().map(VideoMashupDto::getPreviewVideoUrl).orElse(null);
        if (StringUtils.isBlank(previewVideoUrl)) {
            //生成预览视频
            AsyncUtils.runAsync(() -> this.draftVideoMashupPreview(jwtPayload, draft.getId()));
        }

        return result;
    }

    @Override
    public VideoProjectVideoMashupDetail draftVideoMashupPreview(JwtPayload jwtPayload, Long draftId) {
        List<VideoMashupScript> videoMashupScripts = videoMashupScriptManager.getByDraftId(draftId);

        String scriptIds = videoMashupScripts.stream().map(VideoMashupScript::getId).map(Object::toString)
                .collect(Collectors.joining(InngkeAppConst.COMMA_STR));
        Lock lock = lockService.getLock(CrmServiceConsts.CACHE_LOCK_KEY_PRE + "draftVideoMashupPreview:" + Md5Utils.md5(scriptIds), 300);
        if (lock == null) {
            logger.info("预览视频正在生成");
            return null;
        }

        // 1. 获取草稿信息
        VideoProjectDraft draft = videoProjectDraftManager.getById(draftId);
        if (draft == null) {
            throw new InngkeServiceException("草稿不存在");
        }

        // 2. 获取草稿的项目内容，从中提取视频素材数据
        VideoCreateWithVideoMashupRequest projectContent = JsonUtil.jsonToObject(draft.getProjectContext(), VideoCreateWithVideoMashupRequest.class);
        List<OralVideoMaterial> mashupVideoList = FormDataUtils.getOralVideoMaterialList(
                projectContent.getPromptMap(), FormDataUtils.FORM_KEY_ORAL_VIDEO_MATERIAL
        );

        if (CollectionUtils.isEmpty(mashupVideoList)) {
            throw new InngkeServiceException("未找到混剪视频素材");
        }
        Map<Long, String> materialUrlMap = mashupVideoList.stream().collect(Collectors.toMap(OralVideoMaterial::getMaterialId, OralVideoMaterial::getUrl, (v1, v2) -> v1));

        // 3. 获取当前草稿的脚本列表
        if (CollectionUtils.isEmpty(videoMashupScripts)) {
            throw new InngkeServiceException("未找到混剪脚本");
        }
        videoMashupScripts = videoMashupScripts.stream().filter(
                videoMashup -> StringUtils.isBlank(videoMashup.getPreviewVideoUrl())
        ).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(videoMashupScripts)) {
            return getVideoMashupDraftDetail(jwtPayload, draftId);
        }

        // 构建预览请求并调用外部接口
        PreviewMashupVideoRequest previewMashupVideoRequest = new PreviewMashupVideoRequest();
        previewMashupVideoRequest.setTaskId(draftId);
        previewMashupVideoRequest.setScriptIdMaterialMap(Maps.newHashMap());
        videoMashupScripts.forEach(videoMashup -> {
            List<VideoMashupScriptDto> mashupScripts = JsonUtil.jsonToList(videoMashup.getScripts(), VideoMashupScriptDto.class);
            List<VideoMaterialItem> mashupMaterialList = mashupScripts.stream().map(mashupScript -> {
                VideoMaterialItem material = new VideoMaterialItem();
                material.setMaterialId(mashupScript.getId());
                material.setClipStart(mashupScript.getClipStart());
                material.setClipDuration(mashupScript.getClipDuration());
                material.setUrl(materialUrlMap.get(mashupScript.getId()));
                return material;
            }).collect(Collectors.toList());
            previewMashupVideoRequest.getScriptIdMaterialMap().put(videoMashup.getId(), mashupMaterialList);
        });
        BaseResponse<PreviewMashupVideoDto> response = videoApi.previewMashupVideo(previewMashupVideoRequest);
        Map<Long, String> previewVideoUrlMap = Optional.ofNullable(response).map(BaseResponse::getData).map(
                PreviewMashupVideoDto::getPreviewVideoUrlMap).orElse(null);

        // 5. 更新video_mashup_script表的preview_video_url字段
        List<VideoMashupScript> updateList = videoMashupScripts.stream().map(script -> {
            if (!previewVideoUrlMap.containsKey(script.getId())) {
                return null;
            }

            VideoMashupScript updateScript = new VideoMashupScript();
            updateScript.setId(script.getId());
            updateScript.setPreviewVideoUrl(previewVideoUrlMap.get(script.getId()));
            return updateScript;
        }).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(updateList)) {
            videoMashupScriptManager.updateBatchById(updateList);
        }

        // 6. 调用getVideoMashupDraftDetail方法返回结果
        return getVideoMashupDraftDetail(jwtPayload, draftId);
    }

    @Override
    public Map<Long, String> getDraftVideoMashupPreview(JwtPayload jwtPayload, Long draftId) {
        return videoMashupScriptManager.getByDraftId(draftId).stream().collect(Collectors.toMap(VideoMashupScript::getId, VideoMashupScript::getPreviewVideoUrl));
    }

    @Override
    public VideoProjectDraftDto getDraftBasicInfo(JwtPayload jwtPayload, Long draftId) {
        VideoProjectDraft draft = videoProjectDraftManager.getOne(
                Wrappers.<VideoProjectDraft>query()
                        .eq(VideoProjectDraft.ID, draftId)
                        .select(VideoProjectDraft.ID, VideoProjectDraft.TITLE, VideoProjectDraft.TYPE)
        );
        if (draft == null) {
            throw new InngkeServiceException("草稿不存在");
        }
        VideoProjectDraftDto videoProjectDraftDto = new VideoProjectDraftDto();
        videoProjectDraftDto.setId(draft.getId());
        videoProjectDraftDto.setTitle(draft.getTitle());
        videoProjectDraftDto.setType(draft.getType());

        return videoProjectDraftDto;
    }

    private SubtitleConfig getSubtitleConfig(Map<String, Object> promptMap) {
        return VideoCreateServiceImpl.getBaseSubtitleConfig(promptMap, SubtitleConfig.class, null);
    }


    private void updateCoverImage(VideoCreateWithMaterialRequest request, VideoProjectDraft draft) {
        if (CollectionUtils.isEmpty(request.getScripts())) {
            return;
        }
        Map<String, Object> promptMap = request.getPromptMap();
        int videoType = FormDataUtils.getInt(promptMap, FormDataUtils.FORM_KEY_VIDEO_TYPE, 1);

        //实拍
        if (videoType == 2) {
            return;
        }

        //获取第一个镜头
        VideoUserScriptDto firstScript = request.getScripts().stream().findFirst().orElse(null);
        if (Objects.isNull(firstScript)) {
            return;
        }

        //情景/口播视频
        if (!CollectionUtils.isEmpty(firstScript.getSceneMaterialList())) {
            firstScript.getSceneMaterialList().stream().findFirst().ifPresent(
                    firstMaterial -> asyncSaveDraftCoverImage(
                            draft.getId(), firstMaterial.getMaterialId(), firstMaterial.getUrl(), firstMaterial.getClipStart()
                    )
            );
            return;
        }

        //其他 混剪
        if (CollectionUtils.isEmpty(firstScript.getMaterialList()) && draft.getType() == 2) {
            return;
        }
        Optional<VideoMaterialItem> firstMaterialOptional;
        //裂变取备选素材
        if (draft.getType() == 1) {
            firstMaterialOptional = firstScript.getMaterialOriginList().stream().findFirst();
        } else {
            firstMaterialOptional = firstScript.getMaterialList().stream().findFirst();
        }

        VideoDigitalHumanConfig videoDigitalHumanConfig = CollectionUtils.isEmpty(request.getDigitalHumanConfigs()) ? null : request.getDigitalHumanConfigs().stream().findFirst().orElse(null);

        //是否选择数字人
        boolean selectedDigitalHuman = Objects.nonNull(videoDigitalHumanConfig) && Objects.nonNull(videoDigitalHumanConfig.getTemplateId())
                && videoDigitalHumanConfig.getTemplateId() != 0;

        //第一个镜头是否开启了数字人
        boolean isUseDigitalHuman = FormDataUtils.getBoolean(promptMap, FormDataUtils.FORM_KEY_DIGITAL_PERSON_OPEN, false) &&
                Objects.nonNull(firstScript.getDigitalPersonDisplay()) && firstScript.getDigitalPersonDisplay() == 2;

        //没有选择数字人 用第一个镜头的素材
        if (selectedDigitalHuman && isUseDigitalHuman) {
            DigitalPersonTemplate digitalPersonTemplate = digitalPersonTemplateManager.getById(videoDigitalHumanConfig.getTemplateId());
            //获取到数字人配置，并且配置了预览图
            if (Objects.nonNull(digitalPersonTemplate) && StringUtils.isNotBlank(digitalPersonTemplate.getFullScreenPreview())) {
                videoProjectDraftManager.update(
                        Wrappers.<VideoProjectDraft>update()
                                .eq(VideoProjectDraft.ID, request.getDraftId())
                                .set(VideoProjectDraft.COVER_IMAGE, digitalPersonTemplate.getFullScreenPreview())
                );
                return;
            }
        }

        firstMaterialOptional.ifPresent(
                firstMaterial -> asyncSaveDraftCoverImage(
                        draft.getId(), firstMaterial.getMaterialId(), firstMaterial.getUrl(), firstMaterial.getClipStart()
                )
        );
    }

    private void asyncSaveDraftCoverImage(Long draftId, Long materialId, String videoUrl, int clipTime) {
        AsyncUtils.runAsync(() -> {
            Map<Long, Integer> rotateMap = videoMaterialService.getRotate(Sets.newHashSet(materialId));
            String coverImage = screenshot(videoUrl, clipTime, rotateMap.getOrDefault(materialId, 0));
            if (StringUtils.isBlank(coverImage)) {
                return;
            }

            videoProjectDraftManager.update(
                    Wrappers.<VideoProjectDraft>update()
                            .eq(VideoProjectDraft.ID, draftId)
                            .set(VideoProjectDraft.COVER_IMAGE, coverImage)
            );
        });
    }

    @Override
    public String screenshot(String videoUr, int time, int rotate) {
        String name = Md5Utils.md5(videoUr + time);
        BaseResponse<String> resp = videoApi.screenshot(
                new VideoScreenshotRequest()
                        .setUrl(videoUr)
                        .setWidth(1080)
                        .setPath("video/draft/" + name + ".jpg")
                        .setTime(time)
                        .setRotate(rotate)
        );

        if (BaseResponse.responseSuccessWithNonNullData(resp)) {
            return resp.getData();
        }
        logger.error("截屏失败：{}", resp.getMsg());
        return null;
    }

    @Override
    public VideoProjectDraftDetail getDraftDetail(JwtPayload jwtPayload, Long draftId) {
        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }

        if (draftId == 0L) {
            return getDefaultDraft(jwtPayload.getCid());
        }

        VideoProjectDraft draft = videoProjectDraftManager.getById(draftId);
        if (draft == null) {
            throw new InngkeServiceException("草稿不存在");
        }

        if (staff.getTester() == null || !staff.getTester()) {
            //不是测试账号时，不允许跨企业
            if (!draft.getOrganizeId().equals(staff.getOrganizeId()) && draft.getRecommend() <= 0) {
                throw new InngkeServiceException("草稿不存在或无权限访问");
            }
        }

        VideoProjectDraftDetail projectDraftDetail = VideoProjectDraftConverter.toVideoProjectDraftDetail(draft);
        setProjectMaterialRotate(projectDraftDetail.getProjectContent());
        return projectDraftDetail;
    }

    private void setProjectMaterialRotate(VideoCreateWithMaterialRequest request) {
        Map<Long, List<VideoMaterialItem>> materialMap = Maps.newHashMap();
        addMaterial(materialMap, request.getBeforeVideo());
        addMaterial(materialMap, request.getAfterVideo());

        if (CollectionUtils.isEmpty(request.getScripts())) {
            return;
        }
        request.getScripts().forEach(script -> {
            if (script == null) {
                return;
            }
            addMaterial(materialMap, script.getMaterialList());
            addMaterial(materialMap, script.getMaterialOriginList());
            addSceneMaterial(materialMap, script.getSceneMaterialList());
            addSceneMaterial(materialMap, script.getSceneMaterialOriginList());
        });
        if (materialMap.isEmpty()) {
            return;
        }

        videoMaterialService.setMaterialRotate(materialMap);
    }

    private void addSceneMaterial(Map<Long, List<VideoMaterialItem>> materialMap, List<VideoSceneMaterialItem> materials) {
        if (CollectionUtils.isEmpty(materials)) {
            return;
        }
        materials.forEach(material -> addMaterial(materialMap, material));
    }

    private void addMaterial(Map<Long, List<VideoMaterialItem>> materialMap, List<VideoMaterialItem> materials) {
        if (CollectionUtils.isEmpty(materials)) {
            return;
        }
        materials.forEach(material -> addMaterial(materialMap, material));
    }

    private void addMaterial(Map<Long, List<VideoMaterialItem>> materialMap, VideoMaterialItem material) {
        if (material == null || material.getMaterialId() == null || material.getMaterialId().equals(0L)) {
            return;
        }
        materialMap.computeIfAbsent(material.getMaterialId(), materialId -> Lists.newArrayList()).add(material);
    }

    @Override
    public BasePaginationResponse<VideoBgmMaterialDto> getBgmList(JwtPayload jwtPayload, VideoBgmListRequest request) {
        BasePaginationResponse<VideoBgmMaterialDto> response = new BasePaginationResponse<>();

        Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
        if (Objects.isNull(staff)) {
            throw new InngkeServiceException("获取员工信息失败");
        }
        Long organizeId = staff.getOrganizeId();
        Long userId = jwtPayload.getCid();

        QueryWrapper<VideoBgmMaterial> queryWrapper;

        if (!CollectionUtils.isEmpty(request.getIds())) {
            queryWrapper = new QueryWrapper<VideoBgmMaterial>()
                    .eq(VideoBgmMaterial.STATUS, 1)
                    .in(VideoBgmMaterial.ID, request.getIds());
        } else {
            queryWrapper = new QueryWrapper<VideoBgmMaterial>()
                    .eq(VideoBgmMaterial.STATUS, 1);

            // 根据类型过滤
            if (request.getType() != null) {
                if (BgmServiceImpl.MY_MUSIC_TYPE.equals(request.getType())) {
                    // 我的音乐：只查询当前用户上传的BGM
                    queryWrapper.eq(VideoBgmMaterial.USER_ID, userId);
                } else {
                    // 其他分类：查询公共BGM和企业BGM，排除用户上传的BGM
                    queryWrapper.in(VideoBgmMaterial.ORGANIZE_ID, Lists.newArrayList(0L, organizeId))
                            .eq(VideoBgmMaterial.USER_ID, 0)
                            .eq(VideoBgmMaterial.TYPE, request.getType());
                }
            } else {
                // 未指定类型：查询公共BGM和企业BGM，排除用户上传的BGM
                queryWrapper.in(VideoBgmMaterial.ORGANIZE_ID, Lists.newArrayList(0L, organizeId))
                        .eq(VideoBgmMaterial.USER_ID, 0);
            }

            // 添加关键词搜索
            if (StringUtils.isNotBlank(request.getKeyword())) {
                queryWrapper.like(VideoBgmMaterial.FILE_NAME, request.getKeyword());
            }
        }

        response.setTotal(videoBgmMaterialManager.count(queryWrapper));
        response.setList(Lists.newArrayList());

        if (response.getTotal() > 0) {
            int offset = (request.getPageNo() - 1) * request.getPageSize();

            List<VideoBgmMaterial> list = videoBgmMaterialManager.list(
                    queryWrapper.orderByDesc(VideoBgmMaterial.UPDATE_TIME)
                            .orderByDesc(VideoBgmMaterial.SORT_ORDER)
                            .last("LIMIT " + offset + "," + request.getPageSize())
            );
            response.setList(list.stream().map(VideoBgmMaterialConverter::toVideoBgmMaterialDto).collect(Collectors.toList()));
        }

        return response;
    }

    @Override
    public List<VideoDigitalHumanConfig> getDigitalHumanConfig(JwtPayload jwtPayload, VideoDigitalHumanRequest request) {
        DigitalPersonRolesDto scriptsRoles = getScriptsRoles(jwtPayload.getCid(), request.getScriptContent());
        return getDigitalHumanConfigs(jwtPayload, request, scriptsRoles);
    }

    private List<VideoDigitalHumanConfig> getDigitalHumanConfigs(JwtPayload jwtPayload, VideoDigitalHumanRequest request, DigitalPersonRolesDto scriptsRoles) {
        if (Objects.nonNull(request.getDigitalHumanCount()) && scriptsRoles.getScriptsType().equals(request.getDigitalHumanCount())) {
            return null;
        }

        List<VideoDigitalHumanConfig> historyDigitalHumanConfigs = getHistoryDigitalHumanConfig(jwtPayload.getCid(), scriptsRoles.getScriptsType());
        if (!CollectionUtils.isEmpty(historyDigitalHumanConfigs) && !Boolean.TRUE.equals(request.getForce())) {
            for (int i = 0; i < scriptsRoles.getScriptsRole().size(); i++) {
                historyDigitalHumanConfigs.get(i).setName(scriptsRoles.getScriptsRole().get(i));
            }
            List<Long> dpIds = historyDigitalHumanConfigs.stream().map(VideoDigitalHumanConfig::getTemplateId).filter(Objects::nonNull).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(dpIds)) {
                List<Long> existDpIds = digitalPersonTemplateManager.listByIds(dpIds).stream().map(DigitalPersonTemplate::getId).collect(Collectors.toList());
                historyDigitalHumanConfigs.forEach(historyDigitalHumanConfig -> {
                    if (!existDpIds.contains(historyDigitalHumanConfig.getTemplateId())) {
                        historyDigitalHumanConfig.setTemplateId(null);
                    }
                });
            }
            return historyDigitalHumanConfigs;
        }

        Queue<VideoAudioConfig> digitalHumanAudioConfigs = randomGetPairAudioConfig(scriptsRoles.getScriptsRole().size());
        return scriptsRoles.getScriptsRole().stream().map(role -> {
            VideoDigitalHumanConfig digitalHumanConfig = new VideoDigitalHumanConfig();
            digitalHumanConfig.setAudioConfig(digitalHumanAudioConfigs.poll());
            digitalHumanConfig.setName(role);
            return digitalHumanConfig;
        }).collect(Collectors.toList());
    }

    @Override
    public VideoSubtitleBaseInfoResponse getVideoSubtitleBaseInfo(JwtPayload jwtPayload, VideoDigitalHumanRequest request) {
        VideoSubtitleBaseInfoResponse resp = new VideoSubtitleBaseInfoResponse();

        DigitalPersonRolesDto scriptsRoles = getScriptsRoles(jwtPayload.getCid(), request.getScriptContent());
        List<VideoDigitalHumanConfig> digitalHumanConfigs = getDigitalHumanConfigs(jwtPayload, request, scriptsRoles);
        resp.setDigitalHumanConfigs(digitalHumanConfigs);

        if (!CollectionUtils.isEmpty(digitalHumanConfigs) && digitalHumanConfigs.get(0).getAudioConfig() != null) {
            //有数字人配置时
            resp.setFormattedSubtitle(scriptsRoles.getFormattedSubtitle());
        }

        return resp;
    }

    @Override
    public VideoScriptsDto createScript(JwtPayload jwtPayload, VideoCreateWithMaterialRequest request) {
        Map<String, Object> promptMap = request.getPromptMap();
        String asides = (String) promptMap.get(FormDataUtils.FORM_KEY_ASIDES);
        if (StringUtils.isBlank(asides)) {
            throw new InngkeServiceException("视频字幕不能为空！");
        }
        String userId = difyService.getUser(jwtPayload.getCid());
        DifyRequestInputsBuilder difyRequestInputsBuilder = DifyRequestInputsBuilder.newBuilder()
                .set("script", asides);

        int appId = Integer.parseInt(Optional.ofNullable(promptMap.get(FormDataUtils.FORM_KEY_APP_ID))
                .orElse("0").toString());
        if (appId > 0) {
            DifyAppConf difyApp = difyAppConfService.getVideoAppConf(appId);
            if (difyApp != null) {
                difyRequestInputsBuilder.set("class_name", difyApp.getName());
            }
        }

        DifyVideoScriptsResp resp = RetryTaskUtils.process(o -> videoSubtitleTypesetApp.execute(userId, difyRequestInputsBuilder.build()));

        if (resp == null) {
            //生成失败
            throw new InngkeServiceException("脚本分镜失败");
        }

        String audioFile = FormDataUtils.getString(promptMap, FormDataUtils.FORM_KEY_AUDIO_FILE);
        List<String> roles = resp.getRoles();
        if (StringUtils.isNotBlank(audioFile)) {
            //指定了音频
            roles = Lists.newArrayList("旁白");
        }

        DigitalPersonRolesDto rolesRequest = new DigitalPersonRolesDto();
        rolesRequest.setScriptsRole(roles);
        rolesRequest.setScriptsType(roles.size());

        VideoDigitalHumanRequest videoDigitalHumanRequest = new VideoDigitalHumanRequest();
        videoDigitalHumanRequest.setDigitalHumanCount(null);

        List<VideoDigitalHumanConfig> digitalHumanConfigs = getDigitalHumanConfigs(jwtPayload, videoDigitalHumanRequest, rolesRequest);

        VideoScriptsDto dto = new VideoScriptsDto()
                .setRoles(roles)
                .setSubtitles(resp.getSubtitles())
                .setDigitalHumanConfigs(digitalHumanConfigs)
                .setPromptMap(promptMap);

        promptMap.put(FormDataUtils.FORM_KEY_VIDEO_TITLE, resp.getTitle());
        promptMap.put(FormDataUtils.FORM_KEY_VIDEO_CONTENT, resp.getSummary());
        promptMap.put(FormDataUtils.FORM_KEY_VIDEO_TAGS, resp.getTags());
//        promptMap.put(FormDataUtils.FORM_KEY_VIDEO_WORD, resp.getBigTitle());
//        promptMap.put(FormDataUtils.FORM_KEY_COVER_TITLE, resp.getCover());
        if (request.getDraftId() == null || request.getDraftId() == 0) {
            dto.setDraftId(snowflakeIdService.getId());
            request.setDraftId(dto.getDraftId());
        } else {
            dto.setDraftId(request.getDraftId());
        }

        //临时任务ID，与草稿ID一致
        dto.setTaskId(dto.getDraftId());

        dto.setBeforeScript(request.getBeforeScript());
        dto.setAfterScript(request.getAfterScript());

        //自动保存创作草稿
        saveDraft(jwtPayload, request, resp.getTitle());

        return dto;
    }

    @Override
    public Boolean draftDelete(JwtPayload jwtPayload, Long draftId) {
        Staff staff = staffService.getStaffByUserIdNotExistThrow(jwtPayload);

        return videoProjectDraftManager.remove(
                Wrappers.<VideoProjectDraft>query()
                        .eq(VideoProjectDraft.ID, draftId)
                        .eq(VideoProjectDraft.STAFF_ID, staff.getId())
        );
    }

    /**
     * 随机获取音色
     */
    private Queue<VideoAudioConfig> randomGetPairAudioConfig(int count) {
        Queue<VideoAudioConfig> result = Queues.newArrayDeque();
        TtsConfig ttsConfig = ttsConfigManager.randomGetOne(null);
        result.add(toDigitalHumanAudioConfig(ttsConfig));

        for (int i = 1; i < count; i++) {
            ttsConfig = ttsConfigManager.randomGetOne(ttsConfig.getGender() == 1 ? 2 : 1);
            result.add(toDigitalHumanAudioConfig(ttsConfig));
        }
        return result;
    }

    private VideoAudioConfig toDigitalHumanAudioConfig(TtsConfig ttsConfig) {
        VideoAudioConfig videoAudioConfig = new VideoAudioConfig();
        videoAudioConfig.setId(ttsConfig.getId());
        videoAudioConfig.setAvatar(ttsConfig.getImageUrl());
        videoAudioConfig.setName(ttsConfig.getTitle());
        videoAudioConfig.setVoice(ttsConfig.getVoiceType());
        videoAudioConfig.setStyle(ttsConfig.getStyle());
        videoAudioConfig.setPlatform(ttsConfig.getPlatform());
        videoAudioConfig.setSpeedRatio((int) (ttsConfig.getSpeedRatio() * 100));
        videoAudioConfig.setVolume(0.0);
        return videoAudioConfig;
    }

    private List<VideoDigitalHumanConfig> getHistoryDigitalHumanConfig(Long cid, Integer count) {
        HashOperations operations = redisTemplate.opsForHash();
        Object config = operations.get(DIGITAL_PERSON_CONFIG_CACHE_KEY, cid + InngkeAppConst.MIDDLE_LINE_STR + count);
        if (Objects.isNull(config)) {
            return null;
        }

        return JsonUtil.jsonToList(config.toString(), VideoDigitalHumanConfig.class);
    }

    public void cacheDigitalHumanConfig(Long cid, List<VideoDigitalHumanConfig> configs) {
        if (CollectionUtils.isEmpty(configs)) {
            return;
        }

        int count = configs.size();
        redisTemplate.opsForHash().put(
                DIGITAL_PERSON_CONFIG_CACHE_KEY,
                cid + InngkeAppConst.MIDDLE_LINE_STR + count,
                JsonUtil.toJsonString(configs)
        );
    }

    @Override
    public VideoSubtitleGetResponse videoSubtitleGet(JwtPayload jwtPayload, VideoSubtitleGetRequest request) {
        List<String> subtitles = request.getUrls().stream()
                .map(this::getVideoSubtitle)
                .collect(Collectors.toList());

        VideoSubtitleGetResponse data = new VideoSubtitleGetResponse();

        data.setSubtitle(String.join(InngkeAppConst.TURN_LINE, subtitles));

        return data;
    }

    @Override
    public VideoSubtitleGetResponse videoSubtitleRepair(JwtPayload jwtPayload, VideoSubtitleRepairRequest request) {
        Map<String, String> inputs = DifyRequestInputsBuilder.newBuilder()
                .set("script", request.getSubtitle())
                .set("type", Optional.ofNullable(request.getVideoType()).orElse(1))
                .set("fixScene", Optional.ofNullable(request.getRepairTag()).orElse(InngkeAppConst.EMPTY_STR))
                .set("num", Optional.ofNullable(request.getMaterialCount()).orElse(0))
                .build();

        VideoSubtitleGetResponse data = new VideoSubtitleGetResponse();

        String fixedScript = RetryTaskUtils.process(o -> videoStoreScriptFixerApp.execute(difyService.getUser(jwtPayload.getCid()), inputs));

        if (StringUtils.isNotBlank(fixedScript)) {
            data.setSubtitle(fixedScript);
        }

        return data;
    }

    private String getVideoSubtitle(String url) {
        return RetryTaskUtils.<String, String>process(videoUrl -> {
            BaseResponse<String> resp = videoApi.getVideoSubtitle(new AudioInfoRequest().setUrl(url));
            if (BaseResponse.responseSuccessWithNonNullData(resp)) {
                return resp.getData();
            }
            throw new InngkeServiceException(resp.getMsg());
        });
    }

    private List<VideoUtteranceDto> getVideoUtterance(String url) {
        return RetryTaskUtils.<String, List<VideoUtteranceDto>>process(videoUrl -> {
            BaseResponse<List<VideoUtteranceDto>> resp = videoApi.getVideoUtterance(new AudioInfoRequest().setUrl(url));
            if (BaseResponse.responseSuccessWithNonNullData(resp)) {
                List<VideoUtteranceDto> data = resp.getData();
                data.stream().findFirst().ifPresent(firstUtterance -> {
                    if (firstUtterance.getClipStart() < 300) {
                        firstUtterance.setClipDuration(firstUtterance.getClipDuration() + firstUtterance.getClipStart());
                        firstUtterance.setClipStart(0);
                    }
                });
                return data;
            }
            return Lists.newArrayList();
        });
    }

    @Override
    public boolean checkVideoSubtitle(String url) {
        String subtitle = getVideoSubtitle(url);

        return RetryTaskUtils.process(videoSubtitle -> {
            List<String> sensitiveList = getSensitiveList();
            List<String> hasSensitiveList = sensitiveList.stream().filter(subtitle::contains).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(hasSensitiveList)) {
                logger.info("音频脚本在即创平台审核不通过，请选择通用数字人！{}", Joiner.on(InngkeAppConst.COMMA_STR).join(hasSensitiveList));
                throw new NoRetryException("音频脚本在「即创」审核不通过，请选择「通用」数字人！");
            }
            return true;
        });

    }

    private List<String> getSensitiveList() {
        String sensitive = (String) redisTemplate.opsForValue().get(CrmServiceConsts.CACHE_KEY_PRE + "sensitive");
        if (StringUtils.isNotBlank(sensitive)) {
            return Lists.newArrayList(Splitter.on(InngkeAppConst.COMMA_STR).split(sensitive));
        }
        BaseResponse<SensitiveDto> sensitiveResponse = jiChuangBrowserApi.getSensitive();
        SensitiveDto sensitiveDto = Optional.ofNullable(sensitiveResponse).map(BaseResponse::getData).orElse(null);
        if (Objects.isNull(sensitiveDto)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.isEmpty(sensitiveDto.getDigitalHumanRisk())) {
            return Lists.newArrayList();
        }

        sensitive = Joiner.on(InngkeAppConst.COMMA_STR).join(sensitiveDto.getDigitalHumanRisk());
        redisTemplate.opsForValue().set(CrmServiceConsts.CACHE_KEY_PRE + "sensitive", sensitive, 1, TimeUnit.DAYS);

        return sensitiveDto.getDigitalHumanRisk();
    }

    private VideoSubtitleGetResponse getVcResponse(String taskId) {
        VideoSubtitleGetResponse data = new VideoSubtitleGetResponse();
        String authBearer = VolcProperties.STR_BEARER + volcProperties.getTts().getAuth();
        String appId = volcProperties.getTts().getAppId();
        try {
            Response<VolcAtaQueryResponse> resp = volcTtsApi.vcQuery(authBearer, appId, taskId, "1").execute();
            if (!resp.isSuccessful()) {
                throw new InngkeServiceException("查询音频字幕生成结果失败: " + resp);
            }
            VolcAtaQueryResponse body = resp.body();
            List<VolcAtaUtterance> utterances = body.getUtterances();
            if (CollectionUtils.isEmpty(utterances)) {
                throw new InngkeServiceException("查询音频字幕生成结果为空");
            }
            String subtitle = utterances.stream().map(VolcAtaUtterance::getText).collect(Collectors.joining(InngkeAppConst.EMPTY_STR));
            data.setSubtitle(subtitle);
            return data;
        } catch (Exception e) {
            throw new InngkeServiceException("查询音频字幕生成结果失败", e);
        }
    }

    private DigitalPersonRolesDto getScriptsRoles(Long cid, String script) {
        Map<String, String> inputs = DifyRequestInputsBuilder.newBuilder().set("script", script).build();
        return RetryTaskUtils.process(o -> videoDigitalHumanConfigApp.execute(difyService.getUser(cid), inputs));
    }

    private static String getRoleName(List<String> roles, Integer roleIndex) {
        if (CollectionUtils.isEmpty(roles)) {
            return Character.toString((char) ('A' + roleIndex));
        }
        if (roleIndex == null) {
            roleIndex = 0;
        }
        if (roles.size() <= roleIndex) {
            return Character.toString((char) ('A' + roleIndex));
        }
        return roles.get(roleIndex);
    }

    @Override
    public VideoProjectDraftDetail getDefaultDraft(long userId) {
        String key = CrmServiceConsts.CACHE_KEY_PRE + STR_USER_VIDEO_CONFIG + userId;
        HashOperations hashOps = redisTemplate.opsForHash();
        Map userOpts = hashOps.entries(key);
        if (userOpts == null) {
            userOpts = Maps.newHashMap();
        }

        SubtitleConfig subtitleConfig = (SubtitleConfig) userOpts.get(STR_SUBTITLE_CONFIG);
        if (subtitleConfig == null) {
            subtitleConfig = new SubtitleConfig();
            subtitleConfig.setPositionY(DEFAULT_SUBTITLE_POSITION_Y);
            subtitleConfig.setEffectLevel(2);
            subtitleConfig.setFontName("抖音美好体");
            subtitleConfig.setStyleResourceId(160);
            subtitleConfig.setFontSize(14);
        }

        VideoProjectDraftDetail videoProjectDraftDetail = new VideoProjectDraftDetail();
        videoProjectDraftDetail.setId(SnowflakeHelper.getId());
        VideoCreateWithMaterialRequest videoCreateWithMaterialRequest = new VideoCreateWithMaterialRequest();
        videoCreateWithMaterialRequest.setDraftId(videoProjectDraftDetail.getId());
        HashMap<String, Object> prompt = Maps.newHashMap();
//        TextFont textFont = textFontManager.getOne(Wrappers.<TextFont>query()
//                .eq(TextFont.TYPE, 1)
//                .select(TextFont.NAME)
//                .orderByDesc(TextFont.SORT_ORDER)
//                .last(InngkeAppConst.STR_LIMIT_1)
//        );

//        JianyingResource jyResource = jianyingResourceManager.getOne(
//                Wrappers.<JianyingResource>query()
//                        .eq(JianyingResource.STATUS, 1)
//                        .eq(JianyingResource.MATERIAL_TYPE, FormConfigServiceImpl.TEXT_MATERIAL_TYPE)
//                        .select(JianyingResource.ID)
//                        .orderByDesc(JianyingResource.ORDER_SCORE)
//                        .last(InngkeAppConst.STR_LIMIT_1)
//        );

        Integer audioDenoise = (Integer) userOpts.getOrDefault(FormDataUtils.FORM_KEY_AUDIO_DENOISE, 0);
        String videoLut = (String) userOpts.getOrDefault(FormDataUtils.FORM_KEY_VIDEO_LUT, InngkeAppConst.EMPTY_STR);
        int subtitleTagStyle = (int) userOpts.getOrDefault(FormDataUtils.FORM_KEY_SUBTITLE_TAG_STYLE, 0);
        Integer vertical = (int) userOpts.getOrDefault(FormDataUtils.FORM_KEY_VERTICAL, 3);
        if (vertical == -1) {
            vertical = 3;
        }
        int sceneVideoDisplay = (int) userOpts.getOrDefault(FormDataUtils.FORM_KEY_SCENE_VIDEO_DISPLAY, DigitalPersonDisplayEnum.FULL.getCode());
        int digitalPersonSwitch = (int) userOpts.getOrDefault(FormDataUtils.FORM_KEY_DIGITAL_PERSON_SWITCH, DigitalPersonDisplayEnum.ALL_FLOAT.getCode());
        boolean bgmOpen = (boolean) userOpts.getOrDefault(FormDataUtils.FORM_KEY_BGM_OPEN, true);

        prompt.put(FormDataUtils.FORM_KEY_SUBTITLE_TEXT_STYLE, Optional.ofNullable(subtitleConfig.getStyleResourceId()).orElse(160));
        prompt.put(FormDataUtils.FORM_KEY_SUBTITLE_FONT_NAME, Optional.ofNullable(subtitleConfig.getFontName()).orElse("抖音美好体"));
        prompt.put(FormDataUtils.FORM_KEY_SUBTITLE_FONT_SIZE, Optional.ofNullable(subtitleConfig.getFontSize()).orElse(14));
        prompt.put(FormDataUtils.FORM_KEY_EFFECT_LEVEL, Optional.ofNullable(subtitleConfig.getEffectLevel()).orElse(2));
        prompt.put(FormDataUtils.FORM_KEY_SUBTITLE_POSITION_Y, Optional.ofNullable(subtitleConfig.getPositionY()).orElse(DEFAULT_SUBTITLE_POSITION_Y));
        prompt.put(FormDataUtils.FORM_KEY_TRANSITION_TYPE, userOpts.getOrDefault(FormDataUtils.FORM_KEY_TRANSITION_TYPE, 0));

        prompt.put(FormDataUtils.FORM_KEY_AUDIO_OPEN, true);
        prompt.put(FormDataUtils.FORM_KEY_VIDEO_LUT, videoLut);
        prompt.put(FormDataUtils.FORM_KEY_BGM_VOLUME, -8);
        prompt.put(FormDataUtils.FORM_KEY_DIGITAL_PERSON_SWITCH, digitalPersonSwitch); //数字人：所有浮屏
        prompt.put(FormDataUtils.FORM_KEY_SCENE_VIDEO_DISPLAY, sceneVideoDisplay); //实拍、口播：全屏+空镜
        prompt.put(FormDataUtils.FORM_KEY_SUBTITLE_TAG_STYLE, subtitleTagStyle);
        prompt.put(FormDataUtils.FORM_KEY_VIDEO_TYPE, 1);
        prompt.put(FormDataUtils.FORM_KEY_VERTICAL, vertical);
        prompt.put(FormDataUtils.FORM_KEY_APP_ID, 10116);
        prompt.put(FormDataUtils.FORM_KEY_AUDIO_DENOISE, audioDenoise);
        prompt.put(FormDataUtils.FORM_KEY_BGM_OPEN, bgmOpen);

        prompt.put(FormDataUtils.FORM_KEY_BEAT, Optional.ofNullable(userOpts.get(FormDataUtils.FORM_KEY_BEAT)).orElse(1.5));
        prompt.put(FormDataUtils.FORM_KEY_BGM_SHOCK_END, Optional.ofNullable(userOpts.get(FormDataUtils.FORM_KEY_BGM_SHOCK_END)).orElse(false));

        videoCreateWithMaterialRequest.setPromptMap(prompt);
        videoProjectDraftDetail.setProjectContent(videoCreateWithMaterialRequest);

        return videoProjectDraftDetail;
    }
}
