package com.inngke.ai.crm.client.video;

import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.dto.VideoProject;
import com.inngke.ai.dto.request.VideoGenerateRequest;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.JsonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class VideoCreateServiceForCrm {
    private static final Logger logger = LoggerFactory.getLogger(VideoCreateServiceForCrm.class);

    @Autowired
    private VideoApi videoApi;

    @Autowired
    private JsonService jsonService;

    public boolean createTask(VideoGenerateRequest request) {
        BaseResponse<Boolean> resp = videoApi.createTask(request);
        if (BaseResponse.responseSuccessWithNonNullData(resp)) {
            return true;
        }
        logger.error("视频创作失败, request: {}, resp: {}", jsonService.toJson(request), jsonService.toJson(resp));
        return false;
    }

    public boolean createVideoContent(VideoGenerateRequest request) {
        BaseResponse<Boolean> resp = videoApi.createVideoContent(request);
        if (BaseResponse.responseSuccessWithNonNullData(resp)) {
            return true;
        }
        logger.error("视频脚本创作失败, request: {}, resp: {}", jsonService.toJson(request), jsonService.toJson(resp));
        return false;
    }

    public boolean matchMaterial(VideoGenerateRequest request) {
        BaseResponse<Boolean> resp = videoApi.matchMaterial(request);
        if (BaseResponse.responseSuccessWithNonNullData(resp)) {
            return true;
        }
        logger.error("视频素材匹配失败, request: {}, resp: {}", jsonService.toJson(request), jsonService.toJson(resp));
        return false;
    }

    public VideoProject matchMaterialSync(VideoGenerateRequest request) {
        BaseResponse<VideoProject> resp = videoApi.matchMaterialSync(request);
        if (BaseResponse.responseSuccessWithNonNullData(resp)) {
            return resp.getData();
        }
        logger.error("视频素材匹配失败, request: {}, resp: {}", jsonService.toJson(request), jsonService.toJson(resp));
        throw new InngkeServiceException("素材匹配失败: " + resp.getMsg());
    }

    public VideoProject scriptMatchMaterialSync(VideoGenerateRequest request) {
        BaseResponse<VideoProject> resp = videoApi.scriptMatchMaterialSync(request);
        if (BaseResponse.responseSuccessWithNonNullData(resp)) {
            return resp.getData();
        }
        logger.error("视频素材匹配失败, request: {}, resp: {}", jsonService.toJson(request), jsonService.toJson(resp));
        throw new InngkeServiceException("素材匹配失败: " + resp.getMsg());
    }
}
