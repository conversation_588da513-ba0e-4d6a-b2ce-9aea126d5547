package com.inngke.ai.crm.service.schedule;

import com.inngke.ai.crm.service.DifyAppConfService;
import com.inngke.ip.ai.dify.app.BaseDifyWorkflowApp;
import com.inngke.ip.ai.dify.config.DifyApiConfigure;
import com.inngke.ip.ai.dify.dto.DifyAppConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
public class DifyConfigSchedule {
    @Autowired
    private DifyAppConfService difyAppConfService;

    @Autowired
    private List<BaseDifyWorkflowApp> difyWorkflowAppList;

    @Scheduled(fixedRate = 60000)
    public void refreshDifyConfig() {
        Map<String, DifyAppConfig> configs = difyAppConfService.getAppConfigs().stream().collect(Collectors.toMap(DifyAppConfig::getId, Function.identity()));
        DifyApiConfigure.setAppKeys(configs, difyWorkflowAppList);
    }
}
