package com.inngke.ai.crm.controller.devops;

import com.inngke.ai.crm.dto.request.devops.SaveJianYingResourceStyleRequest;
import com.inngke.ai.crm.dto.request.devops.UpdateJingYingResourceRequest;
import com.inngke.ai.crm.dto.response.devops.JianYingResourceDto;
import com.inngke.ai.crm.dto.response.devops.JianYingResourceStyleDto;
import com.inngke.ai.crm.service.devops.DevOpsJianYingService;
import com.inngke.ai.crm.service.devops.DevOpsStyleService;
import com.inngke.common.dto.response.BaseResponse;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import retrofit2.http.Path;

import java.util.List;

/**
 * @chapter DevOps
 * @section 风格
 */
@RequestMapping("/api/ai/devops/style")
@RestController
public class DevOpsStyleController {

    @Autowired
    private DevOpsStyleService devOpsStyleService;
    @Autowired
    private DevOpsJianYingService devOpsJianYingService;

    /**
     * 获取字幕风格列表
     */
    @GetMapping("/{organizeId:\\d+}/list")
    public BaseResponse<List<JianYingResourceStyleDto>> getStylesList(@PathVariable Long organizeId) {
        return devOpsStyleService.getStylesList(organizeId);
    }

    /**
     * 保存字幕风格
     */
    @PostMapping("/{organizeId:\\d+}")
    public BaseResponse<Boolean> saveStyle(
            @PathVariable Long organizeId, @Validated @RequestBody SaveJianYingResourceStyleRequest request) {
        request.setOrganizeId(organizeId);
        return BaseResponse.success(
                devOpsStyleService.saveStyle(request)
        );
    }

    @PostMapping("/{organizeId:\\d+}/clone/{id:\\d+}")
    public BaseResponse<Boolean> clone(@PathVariable Long organizeId,@PathVariable Long id){
        return BaseResponse.success(
                devOpsStyleService.cloneStyle(organizeId, id)
        );
    }

    /**
     * 删除字幕风格
     */
    @DeleteMapping("/{id:\\d+}")
    public BaseResponse<Boolean> deleteStyle(@PathVariable Long id) {
        return BaseResponse.success(
                devOpsStyleService.deleteStyle(id)
        );
    }

    /**
     * 风格文本类型特效列表
     */
    @GetMapping("/{code:\\d+}/resource-scene/list")
    public BaseResponse<List<JianYingResourceDto>> getResourceSceneList(@PathVariable Long code,@RequestParam(required = false) Integer type) {
        return devOpsJianYingService.getResourceSceneList(code,type);
    }

    /**
     * 新增/编辑 风格文本类型特效
     */
    @PostMapping("/{code:\\d+}/resource-scene")
    public BaseResponse<Boolean> updateResourceScene(
            @PathVariable Integer code, @RequestBody UpdateJingYingResourceRequest request) {
        request.setStyle(code);
        return devOpsJianYingService.updateResourceScene(request);
    }

    /**
     * 删除风格文本类型特效
     */
    @DeleteMapping("/resource-scene/{id:\\d+}")
    public BaseResponse<Boolean> deleteResourceScene(@PathVariable Long id) {
        return devOpsJianYingService.deleteResourceScene(id);
    }

}
