package com.inngke.ai.crm.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.inngke.ai.crm.api.video.VideoApi;
import com.inngke.ai.crm.api.video.VideoMaterialApi;
import com.inngke.ai.crm.api.video.dto.VideoBgmMaterialDbfsRequest;
import com.inngke.ai.crm.api.video.dto.VideoBgmMaterialDbfsResp;
import com.inngke.ai.crm.db.crm.entity.Staff;
import com.inngke.ai.crm.db.crm.entity.VideoBgmMaterial;
import com.inngke.ai.crm.db.crm.manager.VideoBgmMaterialManager;
import com.inngke.ai.crm.dto.request.UserBgmUploadRequest;
import com.inngke.ai.crm.service.BgmService;
import com.inngke.ai.crm.service.StaffService;
import com.inngke.ai.dto.request.AudioInfoRequest;
import com.inngke.ai.dto.response.AudioInfo;
import com.inngke.common.core.utils.AsyncUtils;
import com.inngke.common.dto.JwtPayload;
import com.inngke.common.dto.response.BaseResponse;
import com.inngke.common.exception.InngkeServiceException;
import com.inngke.common.service.SnowflakeIdService;
import com.inngke.common.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class BgmServiceImpl implements BgmService {

    public static final Integer MY_MUSIC_TYPE = (int) Byte.MAX_VALUE; // 我的音乐类型

    @Autowired
    private VideoBgmMaterialManager videoBgmMaterialManager;

    @Autowired
    private SnowflakeIdService snowflakeIdService;

    @Autowired
    private VideoApi videoApi;

    @Autowired
    private VideoMaterialApi videoMaterialApi;

    @Autowired
    private StaffService staffService;

    @Override
    public VideoBgmMaterial uploadUserBgm(JwtPayload jwtPayload, UserBgmUploadRequest request) {
        String url = request.getUrl();
        String fileName = request.getFileName();
        try {
            // 获取员工信息
            Staff staff = staffService.getStaffByUserId(jwtPayload.getCid());
            if (Objects.isNull(staff)) {
                throw new InngkeServiceException("用户信息错误");
            }

            boolean exist = videoBgmMaterialManager.count(Wrappers.<VideoBgmMaterial>query().eq(VideoBgmMaterial.URL, url)
                    .eq(VideoBgmMaterial.USER_ID, jwtPayload.getCid()).eq(VideoBgmMaterial.STATUS, 1)) > 0;
            if (exist) {
                throw new InngkeServiceException("BGM已存在");
            }


            // 生成BGM ID
            long bgmId = snowflakeIdService.getId();

            // 创建BGM实体
            VideoBgmMaterial bgm = new VideoBgmMaterial();
            bgm.setId(bgmId);
            bgm.setUrl(url);
            bgm.setFileName(fileName);
            bgm.setType(MY_MUSIC_TYPE); // 我的音乐分类
            bgm.setUserId(jwtPayload.getCid()); // 设置用户ID
            bgm.setOrganizeId(0L);
            bgm.setStatus(1);
            bgm.setSortOrder(100);

            // 获取音频信息
            AudioInfo audioInfo = getAudioInfo(url);
            if (audioInfo != null) {
                bgm.setDuration(audioInfo.getDuration());
                bgm.setVolume(audioInfo.getVolume());
            }

            LocalDateTime now = LocalDateTime.now();
            bgm.setCreateTime(now);
            bgm.setUpdateTime(now);

            // 保存到数据库
            videoBgmMaterialManager.save(bgm);

            // 异步优化BGM（音量归一化 & 优选片段）
            AsyncUtils.runAsync(() -> this.optimizeBgm(bgm));

            return bgm;
        } catch (Exception e) {
            log.error("用户上传BGM失败", e);
            throw new InngkeServiceException("上传失败: " + e.getMessage());
        }
    }

    @Override
    public Boolean deleteUserBgm(JwtPayload jwtPayload, List<Long> bgmIds) {
        try {
            // 验证BGM归属
            List<VideoBgmMaterial> bgmList = videoBgmMaterialManager.list(
                    Wrappers.<VideoBgmMaterial>query()
                            .in(VideoBgmMaterial.ID, bgmIds)
                            .eq(VideoBgmMaterial.USER_ID, jwtPayload.getCid())
                            .eq(VideoBgmMaterial.STATUS, 1)
            );

            if (bgmList.isEmpty()) {
                throw new InngkeServiceException("未找到可删除的BGM");
            }

            // 软删除BGM
            return videoBgmMaterialManager.update(
                    Wrappers.<VideoBgmMaterial>update()
                            .in(VideoBgmMaterial.ID, bgmIds)
                            .eq(VideoBgmMaterial.USER_ID, jwtPayload.getCid())
                            .set(VideoBgmMaterial.STATUS, 0)
            );
        } catch (Exception e) {
            log.error("用户删除BGM失败", e);
            throw new InngkeServiceException("删除失败: " + e.getMessage());
        }
    }

    @Override
    public VideoBgmMaterial getBgm(long bgmId) {
        return videoBgmMaterialManager.getById(bgmId);
    }

    /**
     * 获取音频信息
     */
    private AudioInfo getAudioInfo(String url) {
        try {
            BaseResponse<AudioInfo> resp = videoApi.getBgmDuration(
                    new AudioInfoRequest().setUrl(url)
            );
            if (BaseResponse.responseSuccessWithNonNullData(resp)) {
                return resp.getData();
            }
        } catch (Exception e) {
            log.warn("获取音频信息失败: url={}", url, e);
        }
        return null;
    }

    /**
     * BGM 音量归一化 & 优选片段
     */
    private void optimizeBgm(VideoBgmMaterial bgm) {
        try {
            log.info("准备音量归一化&片段优选：id={}, url={}", bgm.getId(), bgm.getUrl());
            BaseResponse<VideoBgmMaterialDbfsResp> resp = videoMaterialApi.bgmDbfs(
                    new VideoBgmMaterialDbfsRequest().setUrl(bgm.getUrl())
            );
            if (!BaseResponse.responseSuccessWithNonNullData(resp)) {
                log.warn("归一化失败：{}", JsonUtil.toJsonString(resp));
                return;
            }
            VideoBgmMaterialDbfsResp data = resp.getData();
            videoBgmMaterialManager.update(
                    Wrappers.<VideoBgmMaterial>update()
                            .eq(VideoBgmMaterial.ID, bgm.getId())
                            .set(VideoBgmMaterial.OPTIMIZED_URL, data.getUrl())
                            .set(VideoBgmMaterial.CLIP_START, data.getStart())
                            .set(VideoBgmMaterial.CLIP_DURATION, data.getDuration())
            );
        } catch (Exception e) {
            log.error("BGM优化失败: id={}", bgm.getId(), e);
        }
    }
} 